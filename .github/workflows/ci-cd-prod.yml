name: deploy-to-production-server

on:
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+-prod"

jobs:


  adoptionv2-aibusinessschool-com:
    name: adoptionv2-aibusinessschool-com
    runs-on: vm-adoptionv2web
    steps:
      - name: clone repository
        run: |
          cd /home/<USER>/devops
          sudo rm -rf ${{ github.event.repository.name }}
          sudo git clone --branch main https://${{ secrets.ADOPTIONV2 }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git

      - name: Set environment variables
        run: |
          echo "NODE_ENV=production" > /home/<USER>/devops/.env

      - name: docker build & run
        env:
          NODE_ENV: production
        run: |
          cd /home/<USER>/devops
          sudo -E docker compose build --build-arg NODE_ENV=production
          sudo -E docker compose up -d --force-recreate

      - name: list running docker containers
        run: |
          sudo docker ps

name: deploy-to-development-server

on:
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+-dev"

jobs:

  adoptionv2dev-aibusinessschool-com:
    name: adoptionv2dev-aibusinessschool-com
    runs-on: vm-adoptionv2devweb
    steps:
      - name: clone repository
        run: |
          cd /home/<USER>/devops
          sudo rm -rf ${{ github.event.repository.name }}
          sudo git clone --branch dev https://${{ secrets.ADOPTIONV2 }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git

      - name: Set environment variables
        run: |
          echo "NODE_ENV=development" > /home/<USER>/devops/.env

      - name: docker build & run
        env:
          NODE_ENV: development
        run: |
          cd /home/<USER>/devops
          sudo -E docker compose build --build-arg NODE_ENV=development
          sudo -E docker compose up -d --force-recreate

      - name: list running docker containers
        run: |
          sudo docker ps

# Adoption v2 API

Bu proje, Adoption v2 sisteminin backend API'sini içerir.

## Controller <PERSON><PERSON>ştirme Kılavuzu

Yeni bir controller geliştirirken aşağıdaki kurallara uyulmalıdır:

1. **Temel Yapı**

   - Controller sınıfı `@Controller()` dekoratörü ile işaretlenmelidir
   - Her endpoint için uygun HTTP metod dekoratörü kullanılmalıdır (@Get, @Post, @Put, @Delete)
   - Route path'ler açıklayıcı ve REST standartlarına uygun olmalıdır

2. **Doğrulama ve Güvenlik**

   - Giriş parametreleri için DTO (Data Transfer Object) kullanılmalıdır
   - Class-validator ile input validasyonu yapılmalıdır
   - Gerekli yetkilendirmeler için @Roles() dekoratörü kullanılmalıdır

3. **Dokümantasyon**

   - Her endpoint Swagger dokümantasyonu içermelidir (@ApiOperation, @ApiResponse)
   - Parametreler ve response tipleri belirtilmelidir
   - Örnek request/response yapıları eklenmelidir

4. **Hata Yönetimi**

   - Custom exception handler kullanılmalıdır
   - HTTP status kodları doğru şekilde kullanılmalıdır
   - Hata mesajları açıklayıcı olmalıdır

5. **Test**

   - Her controller için unit test yazılmalıdır
   - E2E testler eklenmelidir
   - Test coverage %80'in üzerinde olmalıdır

6. **Performans**
   - N+1 problemi engellenmelidir
   - Gereksiz database sorguları önlenmelidir
   - Büyük veri setleri için pagination kullanılmalıdır

## Kurulum

1. Bağımlılıkları yükleyin:

```bash
npm install
```

2. Gerekli environment değişkenlerini ayarlayın:

```bash
cp .env.example .env
```

3. Veritabanını hazırlayın:

```bash
npm run migration:run
```

4. Geliştirme sunucusunu başlatın:

```bash
npm run start:dev
```

## API Dokümantasyonu

Swagger dokümantasyonuna erişmek için:

```
http://localhost:3000/api/docs
```

## Test

```bash
# unit testler
npm run test

# e2e testler
npm run test:e2e

# test coverage
npm run test:cov
```

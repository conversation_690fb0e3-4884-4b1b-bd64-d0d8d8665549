FROM node:20-alpine

WORKDIR /app

COPY /adoption-v2-api /app

RUN npm install

EXPOSE 3001

ARG NODE_ENV=development
ENV NODE_ENV=$NODE_ENV

RUN echo "Final NODE_ENV value: $NODE_ENV"

CMD echo "Container starting with NODE_ENV=$NODE_ENV" && \
    if [ "$NODE_ENV" = "production" ]; then \
    echo "Starting in PRODUCTION mode" && \
    npm run start; \
    elif [ "$NODE_ENV" = "pre-production" ]; then \
    echo "Starting in PRE-PRODUCTION mode" && \
    npm run start:preprod; \
    else \
    echo "Starting in DEVELOPMENT mode" && \
    npm run start:dev; \
    fi
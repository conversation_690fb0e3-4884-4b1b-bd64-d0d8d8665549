{"name": "aibs_management_panel_server", "version": "0.0.1", "description": "nodejs server base app", "main": "index.js", "scripts": {"start": "NODE_ENV=production node --env-file=config/production.env index.js", "start:dev": "NODE_ENV=development node --env-file=config/development.env index.js", "start:preprod": "NODE_ENV=pre-production node --env-file=config/pre-production.env index.js", "dev": "NODE_ENV=development nodemon --env-file=config/development.env index.js --ignore 'src/docs/*' --ignore '*.json'", "dev:local": "NODE_ENV=local nodemon --env-file=config/local.env index.js --ignore 'src/docs/*' --ignore '*.json'", "test-cron": "node ./src/utils/testCron.js"}, "keywords": [], "author": "Soner ÇETİN", "license": "MIT", "dependencies": {"@aws-sdk/client-ses": "^3.609.0", "@azure/communication-email": "^1.0.0", "@azure/storage-blob": "^12.26.0", "@langchain/anthropic": "^0.3.12", "@langchain/azure-openai": "^0.0.11", "@langchain/community": "^0.3.27", "@langchain/core": "^0.3.36", "@langchain/openai": "^0.4.2", "aws-sdk": "^2.1656.0", "axios": "^1.7.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.2.1", "jsdom": "^24.1.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.13", "md5": "^2.3.0", "moment": "^2.30.1", "mongoose": "^8.5.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nodemailer": "^6.9.14", "nodemon": "^3.1.4", "pdfkit": "^0.16.0", "qrcode": "^1.5.4", "sha256": "^0.2.0", "slugify": "^1.6.6", "validator": "^13.12.0"}}
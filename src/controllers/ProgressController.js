const Progress = require("../models/ProgressModel");

exports.updateProgress = async (req, res) => {
  try {
    const { courseId } = req.params;
    const userId = req.user._id; // Authentication middleware'den gelen user bilgisi

    let progress = await Progress.findOne({
      user: userId,
      course: courseId,
    });

    if (!progress) {
      // Eğer progress kaydı yoksa yeni bir kayıt oluştur
      progress = await Progress.create({
        user: userId,
        course: courseId,
        chapters: [], // Başlangıç için boş chapters array'i
      });
    }

    // İstek body'sinden gelen verilere göre progress'i güncelle
    const { chapterId, topicId, progressData } = req.body;

    if (progressData.videoProgress) {
      progress.updateVideoProgress(
        chapterId,
        topicId,
        progressData.videoProgress
      );
    }

    if (progressData.completed) {
      progress.markTopicCompleted(chapterId, topicId);
    }

    await progress.save();

    res.status(200).json({
      status: "success",
      data: progress,
    });
  } catch (error) {
    res.status(400).json({
      status: "error",
      message: error.message,
    });
  }
};

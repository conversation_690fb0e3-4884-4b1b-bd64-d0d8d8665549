const { default: slugify } = require("slugify");
const AppTrackingModel = require("../models/AppTrackingModel");
const UserModel = require("../models/UserModel");
const { httpResponse } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

const updateAppTracking = async (req, res, next) => {
  try {
    const { userId, appId, appType, year, month, day, incCount } = req.body;
    if (!userId || !appId || !appType || !year || !month || !day) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Check if userId exists and is not undefined/null/NaN
    if (!userId) {
      return res.status(400).json({ error: "userId is required" });
    }

    // Check if userId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ error: "Invalid userId format" });
    }

    // Convert userId to ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);

    const user = await UserModel.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // upsert + $inc
    const filter = {
      userId: userObjectId,
      appId: String(appId),
      appType: String(appType),
      year: Number(year),
      month: Number(month),
      day: Number(day),
    };
    const updateDoc = {
      $inc: { count: incCount ? Number(incCount) : 1 },
      $set: { updatedAt: Date.now() },
    };
    const options = { upsert: true };

    const result = await AppTrackingModel.updateOne(filter, updateDoc, options);

    // Fetch the updated document
    const updatedDoc = await AppTrackingModel.findOne(filter);

    return res.json({
      message: "Daily usage updated",
      result,
      data: updatedDoc,
    });
  } catch (err) {
    console.error("Error updating monthly usage:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

const getAppTracking = async (req, res, next) => {
  try {
    const { userId, appId, appType, year, month, day } = req.query;
    const filter = {};

    // Add year and month to filter if provided
    if (year) {
      filter.year = Number(year);
    }
    if (month) {
      filter.month = Number(month);
    }
    if (day) {
      filter.day = Number(day);
    }

    // Check if userId is a valid ObjectId
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: "Invalid userId format" });
      }
      filter.userId = new mongoose.Types.ObjectId(userId);
    }

    // Only add appId to filter if it's specifically requested
    if (appId) {
      filter.appId = String(appId);
    }
    if (appType) {
      filter.appType = String(appType);
    }

    // If no userId is provided, group by appId and appType to get total usage
    if (!userId) {
      const aggregateResult = await AppTrackingModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: {
              appId: "$appId",
              appType: "$appType",
              year: "$year",
              month: "$month",
              day: "$day",
            },
            totalCount: { $sum: "$count" },
          },
        },
        {
          $sort: {
            "_id.year": -1,
            "_id.month": -1,
            "_id.day": -1,
            "_id.appId": 1,
          },
        },
      ]);

      const totalUsage = aggregateResult.reduce(
        (acc, item) => acc + item.totalCount,
        0
      );

      return res.json({
        totalUsage,
        details: aggregateResult.map((item) => ({
          appId: item._id.appId,
          appType: item._id.appType,
          year: item._id.year,
          month: item._id.month,
          day: item._id.day,
          count: item.totalCount,
        })),
      });
    }

    // If userId is provided, use the original logic
    const docs = await AppTrackingModel.find(filter).sort({
      year: -1,
      month: -1,
      day: -1,
      appId: 1,
    });
    const totalUsage = docs.reduce((acc, doc) => acc + doc.count, 0);

    return res.json({
      totalUsage,
      details: docs,
    });
  } catch (err) {
    console.error("Error fetching usage:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

const mostUsedApp = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { year, month, day } = req.query;
    const filter = {};

    // Add filters if provided
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: "Invalid userId format" });
      }
      filter.userId = new mongoose.Types.ObjectId(userId);
    }
    
    if (year) {
      filter.year = Number(year);
    }
    if (month) {
      filter.month = Number(month);
    }
    if (day) {
      filter.day = Number(day);
    }
      
    const mostUsedApp = await AppTrackingModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: "$appId",
          totalCount: { $sum: "$count" },
          appType: { $first: "$appType" }
        },
      },
      {
        $sort: {
          totalCount: -1,
        },
      },
      { $limit: 10 }
    ]);

    return res.json({
      success: true,
      data: mostUsedApp.map(app => ({
        appId: app._id,
        appType: app.appType,
        totalCount: app.totalCount
      }))
    });
  } catch (err) {
    console.error("Error fetching most used app:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

const getUsecaseUsageStats = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { year, month, day, appType = "usecase" } = req.query;
    const filter = { appType };

    // Add filters if provided
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: "Invalid userId format" });
      }
      filter.userId = new mongoose.Types.ObjectId(userId);
    }
    
    if (year) {
      filter.year = Number(year);
    }
    if (month) {
      filter.month = Number(month);
    }
    if (day) {
      filter.day = Number(day);
    }

    // Aggregate usecase data by appId and sort by total count
    const usecaseStats = await AppTrackingModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: "$appId",
          totalCount: { $sum: "$count" },
          // Store the first occurrence of each field for reference
          appType: { $first: "$appType" },
          // Optional: If you want to include details about dates
          usageDates: {
            $push: {
              year: "$year",
              month: "$month",
              day: "$day",
              count: "$count"
            }
          }
        },
      },
      {
        $sort: {
          totalCount: -1,
        },
      }
    ]);

    const totalUsage = usecaseStats.reduce(
      (acc, item) => acc + item.totalCount,
      0
    );

    return res.json({
      totalUsage,
      details: usecaseStats.map(item => ({
        appId: item._id,
        appType: item.appType,
        totalCount: item.totalCount,
        usageDates: item.usageDates
      }))
    });
  } catch (err) {
    console.error("Error fetching usecase usage stats:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = { 
  updateAppTracking, 
  getAppTracking, 
  mostUsedApp,
  getUsecaseUsageStats
};

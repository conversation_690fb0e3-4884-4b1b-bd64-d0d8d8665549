require("dotenv").config();

const { isEmail } = require("validator");
const User = require("../models/UserModel");
const { httpResponse, generateUserId, sendEmail } = require("../utils/helpers");
const md5 = require("md5");
const sha256 = require("sha256");
const jwt = require("jsonwebtoken");
const config = require("../../config");
const OTPModel = require("../models/OTPModel");
const fs = require("fs");
const path = require("path");
const APIRequestLimitModel = require("../models/APIRequestLimitModel");

function generateAccessToken(email) {
  return jwt.sign(email, config.JWT_SECRET, { expiresIn: 60 * 6000 });
}

exports.createUser = async (req, res, next) => {
  const {
    name,
    surname,
    email,
    username,
    role,
    company,
    department,
    admin,
    onboarding,
  } = req.body;
  let { password } = req.body;

  try {
    const check = await User.find({ email });

    if (check.length !== 0) {
      return httpResponse(
        res,
        200,
        "error",
        "This email address is already registered."
      );
    }
    if (!email || !isEmail(email)) {
      return httpResponse(
        res,
        200,
        "error",
        "Please log in with a valid email address"
      );
    } else if (!name && !surname) {
      return httpResponse(
        res,
        200,
        "error",
        "Please enter your Name and Surname"
      );
    } else if (!password) {
      return httpResponse(res, 200, "error", "Please enter a valid password");
    }
    let createUserId = generateUserId(name);
    while (User.find({ userId: createUserId }).length === 0) {
      createUserId = generateUserId(name);
    }

    password = md5(md5(password) + sha256(password));

    if (check.length === 0) {
      var token = jwt.sign({ email: email }, config.JWT_SECRET, {
        expiresIn: "24h",
      });
      const user = await User.create({
        name: name,
        surname: surname,
        email: email,
        username: username,
        password: password,
        role: role,
        company: company,
        department: department,
        onboarding: onboarding,
        status: "Not verified",
        token: token,
        create_date: new Date(),
        admin: admin,
      });

      // Kullanıcı için varsayılan API istek limitlerini oluştur
      try {
        // Standart textGeneration endpointi için limit oluştur
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "textGeneration",
        });
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "imageGeneration",
        });
        await APIRequestLimitModel.create({
          userId: user._id,
          endpoint: "videoGeneration",
        });

        console.log("API request limits created for the new user");
      } catch (limitError) {
        console.error("Error creating API request limits:", limitError.message);
        // Limit oluşturma hatası kullanıcı oluşturmayı engellememelidir
      }

      return httpResponse(
        res,
        200,
        "success",
        "Account created successfuly",
        user
      );
    }
  } catch (error) {
    return httpResponse(res, 200, "error", "Something went wrong", error);
  }
};
exports.login = async (req, res, next) => {
  const { email, password, ssoLogin, name, surname } = req.body;

  try {
    const response = await User.find({ email });

    if (response.length === 1) {
      // Kullanıcı bulundu
      let userInfo = await User.findOne({ email });

      // SSO ile giriş yapılıyorsa şifre kontrolü yapmadan devam et
      if (ssoLogin === true) {
        // SSO ile gelen name ve surname bilgilerini kullanıcıya ekle
        if (name || surname) {
          const updateData = {};
          if (name) updateData.name = name;
          if (surname) updateData.surname = surname;

          await User.findOneAndUpdate({ email }, updateData);
          userInfo = await User.findOne({ email });
        }

        const token = generateAccessToken({ email });
        userInfo.token = token;
        const { password, ...userWithoutPassword } = userInfo.toObject();

        return httpResponse(
          res,
          200,
          "success",
          "Successfully logged in with SSO.",
          userWithoutPassword
        );
      } else {
        // Normal giriş işlemi (şifre ile)
        const hash = md5(md5(password) + sha256(password));

        if (userInfo.password === hash) {
          const token = generateAccessToken({ email });
          userInfo.token = token;
          const { password, ...userWithoutPassword } = userInfo.toObject();

          return httpResponse(
            res,
            200,
            "success",
            "Successfully logged in.",
            userWithoutPassword
          );
        } else {
          return httpResponse(
            res,
            404,
            "error",
            "error",
            "Invalid credentials"
          );
        }
      }
    } else {
      // Kullanıcı bulunamadı

      // SSO ile giriş yapılıyorsa ve otomatik kayıt yapılacaksa
      // config.AUTO_PROVISION_SSO_USERS değişkenini kontrol et, varsayılan olarak true kabul et
      if (ssoLogin === true && config.AUTO_PROVISION_SSO_USERS !== false) {
        // Yeni kullanıcı oluştur
        const randomPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = md5(
          md5(randomPassword) + sha256(randomPassword)
        );

        const token = generateAccessToken({ email });

        // SSO'dan gelen name ve surname bilgilerini kullan
        const newUser = await User.create({
          email: email,
          password: hashedPassword,
          name: name || "",
          surname: surname || "",
          role: "user",
          status: "Not verified",
          token: token,
          create_date: new Date(),
          admin: false,
          onboarding: null, // Onboarding gerektiğini belirt
          sso_login: true,
        });

        // Kullanıcı için varsayılan API istek limitlerini oluştur
        try {
          await APIRequestLimitModel.create({
            userId: newUser._id,
            endpoint: "textGeneration",
            // Varsayılan değerler model şemasından gelecek
          });

          console.log("API request limits created for the new SSO user");
        } catch (limitError) {
          console.error(
            "Error creating API request limits for SSO user:",
            limitError.message
          );
          // Limit oluşturma hatası kullanıcı oluşturmayı engellememelidir
        }

        const { password, ...userWithoutPassword } = newUser.toObject();

        return httpResponse(
          res,
          200,
          "success",
          "User created and logged in with SSO.",
          userWithoutPassword
        );
      } else {
        return httpResponse(
          res,
          200,
          "error",
          "This email is not registered to any account"
        );
      }
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.me = async (req, res, next) => {
  const { email } = req.query;
  var token = req.headers.authorization?.split(" ")[1];
  try {
    const user = await User.findOne({ email });

    if (!token) return httpResponse(res, 401, "error", "No token provided.'");
    // jwt.verify(token, process.env.TOKEN_SECRET, function (err, decoded) {
    //   if (err)
    //     return httpResponse(
    //       res,
    //       401,
    //       "error",
    //       "Unauthorized Access",
    //       "Unauthorized Access"
    //     );

    const userData = {
      user: {
        id: user._id,
        displayName: user.name + " " + user.surname,
        email: user.email,
      },
    };
    return httpResponse(res, 200, "Success", "User", userData);
    // });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.loginWithToken = async (req, res, next) => {
  const { token } = req.body;
  const secret = process.env.JWT_SECRET || process.env.TOKEN_SECRET;

  try {
    // Token doğrulama
    if (!token) {
      return httpResponse(res, 400, "error", "Token not found", null);
    }

    // Token'ı doğrula
    let decodedToken;
    try {
      decodedToken = jwt.verify(token, secret);
    } catch (error) {
      return httpResponse(res, 401, "error", "Invalid token", null);
    }

    // Token içeriğinden email bilgisini al
    const email = decodedToken.email;

    if (!email) {
      return httpResponse(res, 400, "error", "Invalid token content", null);
    }

    // Kullanıcıyı veritabanında bul
    const user = await User.findOne({ email });

    if (!user) {
      return httpResponse(res, 404, "error", "User not found", null);
    }

    // Kullanıcının admin yetkisi var mı kontrol et
    if (user.role !== "admin" && user.role !== "Administrator") {
      return httpResponse(
        res,
        403,
        "error",
        "You do not have access to the panel",
        null
      );
    }

    // Panel için yeni bir token oluştur
    const panelToken = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        role: user.role,
        isPanel: true, // Panel için özel bir flag
      },
      secret,
      { expiresIn: "8h" }
    );

    // Başarılı yanıt
    return httpResponse(res, 200, "success", "Panel access granted", {
      accessToken: panelToken,
      user: {
        _id: user._id,
        name: user.name,
        surname: user.surname,
        email: user.email,
        role: user.role,
        status: user.status,
      },
    });
  } catch (error) {
    console.error("loginWithToken error:", error);
    return httpResponse(res, 500, "error", "Server error", error.message);
  }
};
exports.changePassword = async (req, res, next) => {
  const { currentPassword, newPassword, email } = req.body;
  console.log(req.body);
  try {
    const user = await User.findOne({ email: email });

    if (!user) {
      return httpResponse(res, 404, "error", "error", "User not found.");
    }
    const isCurrentPasswordValid =
      user.password === md5(md5(currentPassword) + sha256(currentPassword));
    if (!isCurrentPasswordValid) {
      return httpResponse(
        res,
        400,
        "error",
        "error",
        "Invalid current password."
      );
    }
    const hashedPassword = md5(md5(newPassword) + sha256(newPassword));

    const updatedUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword },
      { new: true }
    );

    if (!updatedUser) {
      return httpResponse(res, 404, "error", "error", "User not found.");
    }

    // E-posta şablonunu oku
    let emailTemplate = fs.readFileSync(
      path.join(__dirname, "../utils/emailTemplates/forgotPasswordChange.html"),
      "utf8"
    );

    // Değişkenleri şablona yerleştir
    emailTemplate = emailTemplate
      .replace("${updatedUser?.name}", updatedUser?.name)
      .replace("${new Date().getFullYear()}", new Date().getFullYear());

    sendEmail({
      from: "<EMAIL>",
      to: email,
      subject: "Password Change",
      html: emailTemplate,
    }).then(() => {
      return httpResponse(
        res,
        200,
        "Success",
        "Password successfuly changed",
        updatedUser
      );
    });
  } catch (error) {
    console.log("error.message:", error.message);
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.sendPasswordResetOTPMail = async (req, res, next) => {
  const { email } = req.body;
  var token = req.headers.authorization?.split(" ")[1];
  try {
    const existingUser = await User.findOne({ email });
    if (!existingUser) {
      throw Error("There is no account for the provided email.");
    }
    if (!existingUser.verified) {
      throw Error("Email hasn't been verified. Check your inbox");
    }

    const otpDetails = {
      email,
      subject: "Password Reset",
      message: "Enter the code below to reset your password.",
      duration: 1,
    };
    const createOTP = await sendOTP(otpDetails);
    return createOTP;
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.forgotPassword = async (req, res, next) => {
  const { email } = req.body;
  try {
    const response = await User.find({ email });

    if (response.length > 0) {
      // Önce varolan OTP'yi sil
      await OTPModel.findOneAndDelete({ email });

      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Yeni OTP oluştur
      await OTPModel.create({
        email,
        otp,
      });

      // E-posta şablonunu oku
      let emailTemplate = fs.readFileSync(
        path.join(__dirname, "../utils/emailTemplates/forgotPassword.html"),
        "utf8"
      );

      // Değişkenleri şablona yerleştir
      emailTemplate = emailTemplate
        .replace("${response[0]?.name}", response[0]?.name)
        .replace("${otp}", otp);

      await sendEmail({
        from: "<EMAIL>",
        to: email,
        subject: "Forgot Password",
        html: emailTemplate,
      }).then((ress) => {
        return httpResponse(res, 200, "success", "mail sended", ress);
      });
    } else {
      return httpResponse(res, 404, "error", "error", "Email not found");
    }
  } catch (error) {
    console.log("error:", error);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.forgotPasswordChange = async (req, res, next) => {
  const { email, password } = req.body;
  try {
    const hashedPassword = md5(md5(password) + sha256(password));
    const updatedUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword },
      { new: true }
    );

    // E-posta şablonunu oku
    let emailTemplate = fs.readFileSync(
      path.join(__dirname, "../utils/emailTemplates/forgotPasswordChange.html"),
      "utf8"
    );

    // Değişkenleri şablona yerleştir
    emailTemplate = emailTemplate
      .replace("${updatedUser?.name}", updatedUser?.name)
      .replace("${new Date().getFullYear()}", new Date().getFullYear());

    sendEmail({
      from: "<EMAIL>",
      to: email,
      subject: "Password Change",
      html: emailTemplate,
    }).then(() => {
      return httpResponse(
        res,
        200,
        "Success",
        "Password successfuly changed",
        updatedUser
      );
    });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.checkOTP = async (req, res, next) => {
  const { email, otp } = req.body;
  try {
    const checkOTP = await OTPModel.findOne({ email, otp });

    if (checkOTP) {
      const responseData = [{ email: checkOTP.email, otp: checkOTP.otp }];
      await OTPModel.findOneAndDelete({ email });
      return httpResponse(res, 200, "success", "OTP matched", responseData);
    } else {
      return httpResponse(res, 404, "error", "OTP not found or expired");
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error
    );
  }
};
exports.verifyAccount = async (req, res, next) => {
  const { email, otp } = req.body;

  try {
    const checkOTP = await OTPModel.findOne({ email, otp });
    if (checkOTP) {
      const updatedUser = await User.findOneAndUpdate(
        { email },
        { status: "Verified" },
        { new: true }
      );
      await OTPModel.findOneAndDelete({ email: email });
      return httpResponse(res, 200, "success", "Account verified", updatedUser);
    } else {
      return httpResponse(res, 404, "error", "OTP not found for this account!");
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.definePassword = async (req, res, next) => {
  const { newPassword, email } = req.body;
  try {
    const hashedPassword = md5(md5(newPassword) + sha256(newPassword));

    const checkUser = await User.findOneAndUpdate(
      { email: email },
      { password: hashedPassword, status: "Verified" },
      { new: true }
    );

    if (checkUser !== undefined) {
      return httpResponse(res, 200, "success", "Password changed successfully");
    }
  } catch (error) {
    console.error("error:", error.message);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.resetPassword = async (req, res, next) => {
  const { email, password, otp } = req.body;
  try {
    const checkOTP = await OTPModel.findOne({ email, otp });

    if (checkOTP) {
      const hashedPassword = md5(md5(password) + sha256(password));
      const updatedUser = await User.findOneAndUpdate(
        { email: email },
        { password: hashedPassword },
        { new: true }
      );
      // E-posta şablonunu oku
      let emailTemplate = fs.readFileSync(
        path.join(
          __dirname,
          "../utils/emailTemplates/forgotPasswordChange.html"
        ),
        "utf8"
      );

      // Değişkenleri şablona yerleştir
      emailTemplate = emailTemplate
        .replace("${updatedUser?.name}", updatedUser?.name)
        .replace("${new Date().getFullYear()}", new Date().getFullYear());

      sendEmail({
        from: "<EMAIL>",
        to: email,
        subject: "Password Change",
        html: emailTemplate,
      }).then(async () => {
        try {
          await OTPModel.findOneAndDelete({ email });
        } catch (error) {
          console.error("error:", error.message);
        }
        return httpResponse(
          res,
          200,
          "Success",
          "Password successfuly changed",
          updatedUser
        );
      });
    } else {
      return httpResponse(res, 404, "error", "OTP not found or expired");
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

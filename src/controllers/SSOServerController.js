const SSOServerClientModel = require('../models/SSOServerClientModel');
const SSOServerAuthCode = require('../models/SSOServerAuthCode');
const UserModel = require('../models/UserModel');
const { httpResponse } = require('../utils/helpers');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const slugify = require('slugify');

const authorize = async (req, res) => {
    try {
        const { client_id, redirect_uri, state, response_type } = req.query;

        // Gerekli alanların kontrolü
        if (!client_id || !redirect_uri) {
            return httpResponse(res, 400, "error", "Missing required parameters");
        }

        // Client doğrulama
        const client = await SSOServerClientModel.findOne({ clientId: client_id });
        if (!client) {
            return httpResponse(res, 400, "error", "Invalid client");
        }

        // Redirect URI doğrulama
        if (!client.redirectUris.includes(redirect_uri)) {
            return httpResponse(res, 400, "error", "Invalid redirect URI");
        }

        // Authorization code oluştur
        const code = crypto.randomBytes(16).toString('hex');
        const authCode = new SSOServerAuthCode({
            code,
            clientId: client_id,
            userId: req.userId, // JWT'den gelen userId
            redirectUri: redirect_uri,
            expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 dakika
            scope: 'genaistack'
        });

        await authCode.save();

        // Auth code'u response olarak dön
        return httpResponse(res, 200, "success", "Authorization code generated", {
            code,
            redirect_uri,
            
        });

    } catch (error) {
        return httpResponse(res, 500, "error", "Authorization failed", error.message);
    }
};

const login = async (req, res) => {
    try {
        const { email, password, client_id, redirect_uri, state } = req.body;

        // Kullanıcı doğrulama
        const user = await UserModel.findOne({ email });
        if (!user || user.password !== password) { // Gerçek uygulamada hash kontrolü yapılmalı
            return httpResponse(res, 401, "error", "Invalid credentials");
        }

        // Authorization code oluştur
        const code = crypto.randomBytes(16).toString('hex');
        const authCode = new SSOServerAuthCode({
            code,
            clientId: client_id,
            userId: user._id,
            redirectUri: redirect_uri,
            expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 dakika
            scope: 'genaistack'
        });

        await authCode.save();

        // Redirect URI'ye yönlendir
        const redirectUrl = new URL(redirect_uri);
        redirectUrl.searchParams.append('code', code);
        if (state) redirectUrl.searchParams.append('state', state);

        res.redirect(redirectUrl.toString());

    } catch (error) {
        return httpResponse(res, 500, "error", "Login failed", error.message);
    }
};

const token = async (req, res) => {
    try {
        const { grant_type, code, redirect_uri, client_id, client_secret } = req.body;

        // Client doğrulama
        const client = await SSOServerClientModel.findOne({ 
            clientId: client_id,
            clientSecret: client_secret
        });

        if (!client) {
            return httpResponse(res, 401, "error", "Invalid client credentials");
        }

        // Authorization code doğrulama
        const authCode = await SSOServerAuthCode.findOne({
            code,
            clientId: client_id, 
        }); 

        if (!authCode || authCode.expiresAt < new Date()) {
            return httpResponse(res, 400, "error", "Invalid or expired code " + authCode + " -> " + new Date());
        }

        // Access token oluştur
        const accessToken = jwt.sign(
            {
                userId: authCode.userId,
                scope: authCode.scope
            },
            process.env.JWT_SECRET,
            { expiresIn: '1h' }
        );

        // Authorization code'u sil
        await SSOServerAuthCode.deleteOne({ _id: authCode._id });

        // Direkt olarak istenen formatta response dön
        return res.status(200).json({
            access_token: accessToken,
            expires_in: 3600,
            token_type: 'Bearer',
            scope: authCode.scope
        });

    } catch (error) {
        return httpResponse(res, 500, "error", "Token generation failed", error.message);
    }
};

const getProtectedResource = async (req, res) => {
    try {
        const userId = req.userId;

        const user = await UserModel.findById(userId);
        if (!user) {
            return httpResponse(res, 404, "error", "User not found");
        }

        // Özel slugify kuralları
        const customSlugify = (str) => {
            return slugify(str, {
                lower: true,      // küçük harfe çevir
                strict: true,     // özel karakterleri kaldır
                replacement: '_'  // - yerine _ kullan
            })
            .replace(/-/g, '_');  // kalan - işaretlerini _ yap
        };

        // Direkt olarak istenen formatta response dön
        return res.status(200).json({
            status: 'success',
            message: 'User info retrieved',
            id: customSlugify(user.platformName || 'adoptionv2dev') + '_' + user._id
        });

    } catch (error) {
        return httpResponse(res, 500, "error", "Failed to get user info", error.message);
    }
};

const getAuthData = async (req, res) => {
    try {
        const { client_id, client_secret } = req.body;

        const client = await SSOServerClientModel.findOne({
            name: 'GenAI Stack'
        });

        // only return clientId and redirectUris
        const data = {
            clientId: client.clientId,
            redirectUris: client.redirectUris
        };

        return httpResponse(res, 200, "success", "Auth data retrieved", data);
        
    } catch (error) {
        return httpResponse(res, 500, "error", "Failed to get auth data", error.message);
    }
};

const createClientCredentials = async (req, res) => {
    try {
        const { applicationUrl } = req.body;

        // URL validation
        if (!applicationUrl) {
            return httpResponse(res, 400, "error", "Application URL is required");
        }

        // Check if URL is from allowed domain
        const allowedDomain = 'genai-stack.aibusinessschool.com';
        try {
            const url = new URL(applicationUrl);
            if (url.hostname !== allowedDomain) {
                return httpResponse(res, 403, "error", "Domain not allowed");
            }
        } catch (e) {
            return httpResponse(res, 400, "error", "Invalid URL format");
        }

        // Generate client credentials
        const clientId = crypto.randomBytes(16).toString('hex');
        const clientSecret = crypto.randomBytes(32).toString('hex');

        // Create new client
        const client = new SSOServerClientModel({
            clientId,
            clientSecret,
            redirectUris: [`${applicationUrl}/callback`],
            name: 'GenAI Stack',
            description: 'GenAI Stack Application'
        });

        await client.save();

        return httpResponse(res, 201, "success", "Client credentials created", {
            clientId,
            clientSecret,
            redirectUris: client.redirectUris
        });

    } catch (error) {
        return httpResponse(res, 500, "error", "Failed to create client credentials", error.message);
    }
};

const getUserStacks = async (req, res) => {
    try {
        const { userId } = req.params;
        
        if (!userId) {
            return httpResponse(res, 400, "error", "userId is required");
        }
        
        // Add a variable declaration for getClientDatabyName
        let getClientDatabyName;
        try {
            getClientDatabyName = await SSOServerClientModel.findOne({ name: "GenAI Stack" });
            
            if (!getClientDatabyName) {
                return httpResponse(res, 404, "error", "GenAI Stack client not found");
            }
        } catch (dbError) {
            console.error("Database error:", dbError);
            return httpResponse(res, 500, "error", "Database operation failed", 
                "Operation `ssoserverclients.findOne()` failed. Please check your database connection.");
        }
        
        const customSlugify = (str) => {
            return slugify(str, {
                lower: true,      // küçük harfe çevir
                strict: true,     // özel karakterleri kaldır
                replacement: '_'  // - yerine _ kullan
            })
            .replace(/-/g, '_');  // kalan - işaretlerini _ yap
        };

        // Find the user to get platformName
        let user;
        try { 
            user = await UserModel.findOne({_id: userId});
          
        } catch (userError) {
            console.error("User lookup error:", userError);
            return httpResponse(res, 500, "error", "Failed to find user", userError.message);
        }
    
        const userID = customSlugify(user.platformName || 'adoptionv2dev') + '_' + userId;

        //return httpResponse(res, 200, "success", "User ID retrieved", userID);
        const dataUrl = 'https://genai-stack.aibusinessschool.com/api/v1/auth/stacks/deployed-count/?user_id=' + userID + '&client_id=' + getClientDatabyName.clientId + '&client_secret=' + getClientDatabyName.clientSecret + '&auth_server=' + getClientDatabyName.redirectUris[0];
       
        try {
            const response = await fetch(dataUrl);
            const data = await response.json();
            return httpResponse(res, 200, "success", "User stacks retrieved", data);
        } catch (fetchError) {
            console.error("API fetch error:", fetchError);
            return httpResponse(res, 500, "error", "Failed to fetch user stacks from API", fetchError.message);
        }
    } catch (error) {
        console.error("getUserStacks error:", error);
        return httpResponse(res, 500, "error", "Failed to get user stacks", error.message);
    }
};

module.exports = { authorize, login, token, getProtectedResource, createClientCredentials, getUserStacks, getAuthData }; 
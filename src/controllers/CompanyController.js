const CompanyModel = require("../models/CompanyModel");
const { httpResponse } = require("../utils/helpers");

exports.addCompany = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  const { companyName, companyImg } = req.body;
  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }
  try {
    const checkCompnayExist = await CompanyModel.findOne({ companyName });
    if (checkCompnayExist === null) {
      const createCompany = await CompanyModel.create({
        companyName,
        companyImg,
      });
      return httpResponse(
        res,
        200,
        "success",
        "Company Created",
        createCompany
      );
    } else {
      return httpResponse(
        res,
        500,
        "error",
        "error",
        "Company already created"
      );
    }
  } catch (err) {
    if (err.name === "JsonWebTokenError") {
      return httpResponse(res, 401, "error", "Unauthorized Access");
    } else {
      console.log("error.message:", err.message);
      return httpResponse(
        res,
        500,
        "error",
        "An internal server error occurred",
        err.message
      );
    }
  }
};
exports.getCompany = async (req, res, next) => {
  const { id } = req.params;
  try {
    if (id) {
      const company = await CompanyModel.findById({ _id: id });
      return httpResponse(res, 200, "Success", "Company Information", company);
    } else {
      const companies = await CompanyModel.find();
      return httpResponse(res, 200, "Success", "All Companies", companies);
    }
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};

exports.deleteCompany = async (req, res, next) => {
  const { id } = req.params;
  try {
    const deletedCompany = await CompanyModel.findByIdAndDelete({ _id: id });
    return httpResponse(res, 200, "Success", "Company Deleted", deletedCompany);
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};
exports.updateCompany = async (req, res, next) => {
  const { id } = req.params;
  const { companyName, companyImg } = req.body;
  try {
    const updatedCompany = await CompanyModel.findByIdAndUpdate(
      { _id: id },
      { companyName, companyImg },
      { new: true }
    );
    return httpResponse(res, 200, "Success", "Company Updated", updatedCompany);
  } catch (error) {
    return httpResponse(res, 500, "error", "Server Error", error.message);
  }
};

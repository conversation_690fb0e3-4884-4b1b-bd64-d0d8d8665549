const LanguageModel = require("../models/LanguageModel");
const { httpResponse } = require("../utils/helpers");
const jwt = require("jsonwebtoken");

exports.addTranslates = async (req, res, next) => {
  const { lang, translates } = req.body;
  var token = req.headers.authorization?.split(" ")[1];

  try {
    const response = await LanguageModel.find({ lang: lang });
    if (response.length < 1) {
      const createTranslates = await LanguageModel.create({
        lang: lang,
        translates: translates,
      });
      if (!token) return httpResponse(res, 401, "error", "No token provided.'");
      jwt.verify(token, process.env.TOKEN_SECRET, function (err, decoded) {
        if (err)
          return httpResponse(
            res,
            401,
            "error",
            "Unauthorized Access",
            "Unauthorized Access"
          );

        return httpResponse(
          res,
          202,
          "Success",
          "Language added",
          createTranslates
        );
      });
    } else {
      return httpResponse(
        res,
        400,
        "error",
        "Language add error",
        "Language has already exist"
      );
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getTranslates = async (req, res, next) => {
  const { lang } = req.body;
  const token = req.headers.authorization?.split(" ")[1];

  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }

  try {
    const response = await LanguageModel.find({ lang });
    if (!response || response.length === 0) {
      return httpResponse(res, 404, "error", "Language not found.");
    }

    return httpResponse(
      res,
      200,
      "Success",
      `${response[0].lang} translates`,
      response[0].translates
    );
  } catch (err) {
    if (err.name === "JsonWebTokenError") {
      return httpResponse(res, 401, "error", "Unauthorized Access");
    } else {
      console.log("error.message:", err.message);
      return httpResponse(
        res,
        500,
        "error",
        "An internal server error occurred",
        err.message
      );
    }
  }
};

exports.editTranslates = async (req, res, next) => {
  const { lang, key, value } = req.body;
  var token = req.headers.authorization?.split(" ")[1];

  try {
    const findLang = await LanguageModel.findOne({ lang: lang.lang });

    if (!findLang) {
      return res.status(404).json({ message: "Language not found" });
    }

    const keys = lang.key.split(".");
    let current = findLang.translates;
    let keyExists = true;

    for (let i = 0; i < keys.length; i++) {
      if (current[keys[i]] === undefined) {
        keyExists = false;
        break;
      }
      if (i === keys.length - 1) {
        current[keys[i]] = lang.value;
      } else {
        current = current[keys[i]];
      }
    }

    if (keyExists) {
      findLang.markModified("translates");
      await findLang.save();
      return res
        .status(200)
        .json({ message: "Translation updated successfully" });
    } else {
      return res.status(404).json({ message: "Key not found" });
    }
  } catch (error) {
    console.log("error.message:", error.message);
    return res.status(500).json({
      status: "error",
      message: "An internal server error occurred",
      error: error.message,
    });
  }
};

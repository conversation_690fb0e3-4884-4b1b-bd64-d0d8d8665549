const APIRequestLimitModel = require("../models/APIRequestLimitModel");
const UserModel = require("../models/UserModel");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");

// Limit süresi dolan kullanıcıları sıfırlayan yardımcı fonksiyon
async function resetExpiredLimits(userId, endpoint) {
  try {
    const userLimit = await APIRequestLimitModel.findOne({ userId, endpoint });

    if (!userLimit) return null;

    const now = new Date();
    let isUpdated = false;
    const updateData = {};

    // Günlük limit resetleme
    if (
      userLimit.dailyUsage.resetDate &&
      userLimit.dailyUsage.resetDate < now
    ) {
      updateData["dailyUsage.count"] = 0;
      updateData["dailyUsage.resetDate"] = new Date(
        new Date().setHours(24, 0, 0, 0)
      ); // Yarın
      isUpdated = true;
    }

    // Haftalık limit resetleme
    if (
      userLimit.weeklyUsage.resetDate &&
      userLimit.weeklyUsage.resetDate < now
    ) {
      updateData["weeklyUsage.count"] = 0;
      // Bugünden sonraki Pazar günü
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + (7 - nextWeek.getDay()));
      nextWeek.setHours(0, 0, 0, 0);
      updateData["weeklyUsage.resetDate"] = nextWeek;
      isUpdated = true;
    }

    // Aylık limit resetleme
    if (
      userLimit.monthlyUsage.resetDate &&
      userLimit.monthlyUsage.resetDate < now
    ) {
      updateData["monthlyUsage.count"] = 0;
      // Bir sonraki ayın ilk günü
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      nextMonth.setHours(0, 0, 0, 0);
      updateData["monthlyUsage.resetDate"] = nextMonth;
      isUpdated = true;
    }

    // Değişiklik varsa kaydet
    if (isUpdated) {
      return await APIRequestLimitModel.findOneAndUpdate(
        { userId, endpoint },
        { $set: updateData },
        { new: true }
      );
    }

    return userLimit;
  } catch (error) {
    console.error("Error resetting limits:", error.message);
    return null;
  }
}

// Tüm kullanıcıların API istek limitlerini listeler
exports.getAllLimits = async (req, res) => {
  try {
    const { page = 1, limit = 10, endpoint, userId } = req.query;
    const skip = (page - 1) * limit;

    // Filtre oluşturma
    const filter = {};
    if (endpoint) filter.endpoint = endpoint;
    if (userId) filter.userId = userId;

    // Toplam kayıt sayısını al
    const total = await APIRequestLimitModel.countDocuments(filter);

    // Limitleri ve ilgili kullanıcı bilgilerini getir
    const limits = await APIRequestLimitModel.find(filter)
      .populate("userId", "name surname email username role")
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ lastUsageDate: -1 });

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "API request limits fetched successfully",
      {
        limits,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit),
        },
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API request limits fetch error",
      { message: error.message }
    );
  }
};

// Belirli bir kullanıcının API istek limitlerini getirir
exports.getUserLimit = async (req, res) => {
  try {
    const { userId, endpoint = "textGeneration" } = req.params;

    let userLimit = await APIRequestLimitModel.findOne({
      userId,
      endpoint,
    }).populate("userId", "name surname email username role");

    if (!userLimit) {
      // Kullanıcının limit kaydı yoksa yeni bir kayıt oluştur
      userLimit = new APIRequestLimitModel({
        userId,
        endpoint,
      });
      await userLimit.save();
      // Yeni oluşturulan kayıt için populate işlemi yap
      userLimit = await APIRequestLimitModel.findOne({
        userId,
        endpoint,
      }).populate("userId", "name surname email username role");
    } else {
      // Süresi dolan limitleri resetle
      userLimit = (await resetExpiredLimits(userId, endpoint)) || userLimit;
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "User limit fetched successfully",
      userLimit
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "User limit fetch error",
      { message: error.message }
    );
  }
};

// Oturum açmış kullanıcının API istek limitlerini getirir
exports.getMyLimits = async (req, res) => {
  try {
    const userEmail = req.user.email;
    const findUser = await UserModel.findOne({ email: userEmail });

    const { endpoint = "textGeneration" } = req.query;

    let userLimit = await APIRequestLimitModel.findOne({
      userId: findUser._id,
      endpoint,
    });

    // Kullanıcının limit kaydı yoksa yeni bir kayıt oluştur ve kaydet
    if (!userLimit) {
      userLimit = new APIRequestLimitModel({
        userId: findUser._id,
        endpoint,
      });
      await userLimit.save();
    } else {
      // Süresi dolan limitleri resetle
      userLimit =
        (await resetExpiredLimits(findUser._id, endpoint)) || userLimit;
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Limits fetched successfully",
      {
        endpoint,
        dailyUsage: {
          count: userLimit.dailyUsage.count,
          limit: userLimit.dailyUsage.limit,
          remaining: Math.max(
            0,
            userLimit.dailyUsage.limit - userLimit.dailyUsage.count
          ),
          resetDate: userLimit.dailyUsage.resetDate,
        },
        weeklyUsage: {
          count: userLimit.weeklyUsage.count,
          limit: userLimit.weeklyUsage.limit,
          remaining: Math.max(
            0,
            userLimit.weeklyUsage.limit - userLimit.weeklyUsage.count
          ),
          resetDate: userLimit.weeklyUsage.resetDate,
        },
        monthlyUsage: {
          count: userLimit.monthlyUsage.count,
          limit: userLimit.monthlyUsage.limit,
          remaining: Math.max(
            0,
            userLimit.monthlyUsage.limit - userLimit.monthlyUsage.count
          ),
          resetDate: userLimit.monthlyUsage.resetDate,
        },
        lastUsageDate: userLimit.lastUsageDate,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Limits fetch error",
      { message: error.message }
    );
  }
};

// Kullanıcının API istek limitlerini günceller
exports.updateUserLimit = async (req, res) => {
  try {
    const { userId, endpoint = "textGeneration" } = req.params;
    const { dailyLimit, weeklyLimit, monthlyLimit } = req.body;

    // Kullanıcı ve endpoint'e göre limiti bul
    let userLimit = await APIRequestLimitModel.findOne({
      userId,
      endpoint,
    });

    // Kullanıcının limiti yoksa yeni oluştur
    if (!userLimit) {
      userLimit = new APIRequestLimitModel({
        userId,
        endpoint,
      });

      // Yeni oluşturulan limitin diğer endpoint'ler için de oluşturulup oluşturulmadığını kontrol et
      const endpoints = [
        "textGeneration",
        "imageGeneration",
        "videoGeneration",
      ];
      const currentEndpoint = endpoints.find((e) => e === endpoint);
      const otherEndpoints = endpoints.filter((e) => e !== currentEndpoint);

      // Diğer endpoint'ler için de kayıtlar oluştur
      for (const otherEndpoint of otherEndpoints) {
        const existingLimit = await APIRequestLimitModel.findOne({
          userId,
          endpoint: otherEndpoint,
        });

        if (!existingLimit) {
          await new APIRequestLimitModel({
            userId,
            endpoint: otherEndpoint,
          }).save();
          console.log(
            `Created API limit for user ${userId} on endpoint ${otherEndpoint}`
          );
        }
      }
    } else {
      // Süresi dolan limitleri resetle
      userLimit = (await resetExpiredLimits(userId, endpoint)) || userLimit;
    }

    // Mevcut sayaçları ve reset tarihlerini hatırla
    const dailyCount = userLimit.dailyUsage.count || 0;
    const weeklyCount = userLimit.weeklyUsage.count || 0;
    const monthlyCount = userLimit.monthlyUsage.count || 0;
    const dailyResetDate = userLimit.dailyUsage.resetDate;
    const weeklyResetDate = userLimit.weeklyUsage.resetDate;
    const monthlyResetDate = userLimit.monthlyUsage.resetDate;

    // Limitleri güncelle
    if (dailyLimit !== undefined && !isNaN(dailyLimit)) {
      userLimit.dailyUsage.limit = parseInt(dailyLimit);
      // Mevcut sayaç ve reset tarihini koru
      userLimit.dailyUsage.count = dailyCount;
      userLimit.dailyUsage.resetDate = dailyResetDate;
    }

    if (weeklyLimit !== undefined && !isNaN(weeklyLimit)) {
      userLimit.weeklyUsage.limit = parseInt(weeklyLimit);
      // Mevcut sayaç ve reset tarihini koru
      userLimit.weeklyUsage.count = weeklyCount;
      userLimit.weeklyUsage.resetDate = weeklyResetDate;
    }

    if (monthlyLimit !== undefined && !isNaN(monthlyLimit)) {
      userLimit.monthlyUsage.limit = parseInt(monthlyLimit);
      // Mevcut sayaç ve reset tarihini koru
      userLimit.monthlyUsage.count = monthlyCount;
      userLimit.monthlyUsage.resetDate = monthlyResetDate;
    }

    // Değişiklikleri kaydet
    await userLimit.save();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "User limit updated successfully",
      userLimit
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "User limit update error",
      { message: error.message }
    );
  }
};

// Kullanıcının API istek sayaçlarını sıfırlar
exports.resetUserCounter = async (req, res) => {
  try {
    const { userId, endpoint = "textGeneration" } = req.params;
    const { resetDaily, resetWeekly, resetMonthly } = req.body;

    // Kullanıcı ve endpoint'e göre limiti bul
    const userLimit = await APIRequestLimitModel.findOne({
      userId,
      endpoint,
    });

    if (!userLimit) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "User limit not found",
        null
      );
    }

    // Otomatik süre kontrolü yap ve sıfırla
    await resetExpiredLimits(userId, endpoint);

    // Manuel sıfırlama talepleri
    const updateData = {};

    if (resetDaily) {
      updateData["dailyUsage.count"] = 0;
    }

    if (resetWeekly) {
      updateData["weeklyUsage.count"] = 0;
    }

    if (resetMonthly) {
      updateData["monthlyUsage.count"] = 0;
    }

    // Değişiklik varsa uygula
    if (Object.keys(updateData).length > 0) {
      await APIRequestLimitModel.findOneAndUpdate(
        { userId, endpoint },
        { $set: updateData }
      );
    }

    // Güncellenmiş veriyi getir
    const updatedLimit = await APIRequestLimitModel.findOne({
      userId,
      endpoint,
    });

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "User counter reset successfully",
      updatedLimit
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "User counter reset error",
      { message: error.message }
    );
  }
};

// Tüm kullanıcıların API istek sayaçlarını sıfırlar (Toplu sıfırlama)
exports.resetAllCounters = async (req, res) => {
  try {
    const {
      endpoint = "textGeneration",
      resetDaily,
      resetWeekly,
      resetMonthly,
    } = req.body;

    const updateData = {};

    if (resetDaily) {
      updateData["dailyUsage.count"] = 0;
    }

    if (resetWeekly) {
      updateData["weeklyUsage.count"] = 0;
    }

    if (resetMonthly) {
      updateData["monthlyUsage.count"] = 0;
    }

    // Güncelleme yapmak için en az bir alan seçilmeli
    if (Object.keys(updateData).length === 0) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "You must select at least one counter to reset (daily/weekly/monthly)",
        null
      );
    }

    // Belirtilen endpoint'teki tüm kullanıcıların sayaçlarını sıfırla
    const filter = endpoint ? { endpoint } : {};
    const result = await APIRequestLimitModel.updateMany(filter, {
      $set: updateData,
    });

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "All user counters reset successfully",
      {
        matched: result.matchedCount,
        modified: result.modifiedCount,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Counter reset error",
      { message: error.message }
    );
  }
};

// Limit sürelerini otomatik resetlemek için planlı görev
// Bu fonksiyon bir planlı görev şeklinde çalıştırılabilir (örn. her gün gece yarısı)
exports.resetExpiredCounters = async (req, res) => {
  try {
    const now = new Date();
    let updates = 0;

    // Günlük reset
    const dailyResult = await APIRequestLimitModel.updateMany(
      { "dailyUsage.resetDate": { $lt: now } },
      {
        $set: {
          "dailyUsage.count": 0,
          "dailyUsage.resetDate": new Date(new Date().setHours(24, 0, 0, 0)), // Yarın
        },
      }
    );
    updates += dailyResult.modifiedCount;

    // Haftalık reset
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + (7 - nextWeek.getDay()));
    nextWeek.setHours(0, 0, 0, 0);

    const weeklyResult = await APIRequestLimitModel.updateMany(
      { "weeklyUsage.resetDate": { $lt: now } },
      {
        $set: {
          "weeklyUsage.count": 0,
          "weeklyUsage.resetDate": nextWeek,
        },
      }
    );
    updates += weeklyResult.modifiedCount;

    // Aylık reset
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    nextMonth.setHours(0, 0, 0, 0);

    const monthlyResult = await APIRequestLimitModel.updateMany(
      { "monthlyUsage.resetDate": { $lt: now } },
      {
        $set: {
          "monthlyUsage.count": 0,
          "monthlyUsage.resetDate": nextMonth,
        },
      }
    );
    updates += monthlyResult.modifiedCount;

    if (res) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Expired counters reset successfully",
        {
          dailyResets: dailyResult.modifiedCount,
          weeklyResets: weeklyResult.modifiedCount,
          monthlyResets: monthlyResult.modifiedCount,
          totalUpdates: updates,
        }
      );
    }

    return {
      dailyResets: dailyResult.modifiedCount,
      weeklyResets: weeklyResult.modifiedCount,
      monthlyResets: monthlyResult.modifiedCount,
      totalUpdates: updates,
    };
  } catch (error) {
    if (res) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "Error resetting expired counters",
        { message: error.message }
      );
    }
    console.error("Error resetting expired counters:", error.message);
    return null;
  }
};

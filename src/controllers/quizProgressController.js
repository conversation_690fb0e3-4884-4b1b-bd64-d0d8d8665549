const QuizProgress = require("../models/QuizProgress");
const UserModel = require("../models/UserModel");
const { errorHandler } = require("../utils/errorHandler");

// Quiz ilerlemesini getir
exports.getQuizProgress = async (req, res) => {
  try {
    const { courseId, chapterId, topicId } = req.params;
    const { email } = req.user;

    // Email'den user'ı bul
    const user = await UserModel.findOne({ email });
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    const progress = await QuizProgress.findOne({
      userId: user._id,
      courseId,
      chapterId,
      topicId,
    });

    // Progress bulunamadıysa başlangıç durumunu dön
    if (!progress) {
      return res.status(200).json({
        completed: false,
        score: 0,
        answers: {},
        attemptCount: 0,
        lastAttemptDate: null,
      });
    }

    res.status(200).json({
      completed: progress.completed,
      score: progress.score,
      answers: progress.answers,
      attemptCount: progress.attemptCount,
      lastAttemptDate: progress.lastAttemptDate,
    });
  } catch (error) {
    console.error("Get Quiz Progress Error:", error);
    errorHandler(res, error);
  }
};

// Quiz ilerlemesini kaydet veya güncelle
exports.saveQuizProgress = async (req, res) => {
  try {
    const { courseId, chapterId, topicId } = req.params;
    const { completed, score, answers } = req.body;
    const { email } = req.user;

    // Email'den user'ı bul
    const user = await UserModel.findOne({ email });
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    const existingProgress = await QuizProgress.findOne({
      userId: user._id,
      courseId,
      chapterId,
      topicId,
    });

    if (existingProgress) {
      // Mevcut ilerlemeyi güncelle
      existingProgress.completed = completed;
      existingProgress.score = Math.max(existingProgress.score, score); // En yüksek skoru sakla
      existingProgress.answers = answers;
      existingProgress.attemptCount += 1;
      existingProgress.lastAttemptDate = new Date();

      await existingProgress.save();

      res.status(200).json({
        message: "Quiz progress updated",
        progress: existingProgress,
      });
    } else {
      // Yeni ilerleme oluştur
      const newProgress = new QuizProgress({
        userId: user._id,
        courseId,
        chapterId,
        topicId,
        completed,
        score,
        answers,
      });

      await newProgress.save();

      res.status(201).json({
        message: "Quiz progress saved",
        progress: newProgress,
      });
    }
  } catch (error) {
    console.error("Save Quiz Progress Error:", error);
    errorHandler(res, error);
  }
};

// Quiz istatistiklerini getir
exports.getQuizStats = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { email } = req.user;

    // Email'den user'ı bul
    const user = await UserModel.findOne({ email });
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    const stats = await QuizProgress.aggregate([
      {
        $match: {
          userId: user._id,
          courseId: courseId,
          completed: true,
        },
      },
      {
        $group: {
          _id: null,
          averageScore: { $avg: "$score" },
          totalAttempts: { $sum: "$attemptCount" },
          completedQuizzes: { $sum: 1 },
        },
      },
    ]);

    res.status(200).json(
      stats[0] || {
        averageScore: 0,
        totalAttempts: 0,
        completedQuizzes: 0,
      }
    );
  } catch (error) {
    console.error("Get Quiz Stats Error:", error);
    errorHandler(res, error);
  }
};

const PlatformSettingsModel = require("../models/PlatformSettingsModel");
const { httpResponse } = require("../utils/helpers");

exports.getPublicSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne();
    const publicSettings = {
      platformName: settings?.general?.platformName,
      platformLogo: settings?.general?.platformLogo,
      platformURL: settings?.general?.platformURL,
      ssoEnabled: settings?.ssoSettings?.isEnabled,
    };

    return httpResponse(
      res,
      200,
      "success",
      "Public Settings Retrieved",
      publicSettings
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.getGeneralSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne({}, "general");
    return httpResponse(
      res,
      200,
      "success",
      "General Settings Retrieved",
      settings?.general || {}
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.getUserSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne({}, "user");
    return httpResponse(
      res,
      200,
      "success",
      "User Settings Retrieved",
      settings?.user || {}
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.getSecuritySettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne({}, "security");
    return httpResponse(
      res,
      200,
      "success",
      "Security Settings Retrieved",
      settings?.security || {}
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};
exports.getSsoSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne({}, "ssoSettings");
    return httpResponse(
      res,
      200,
      "success",
      "SSO Settings Retrieved",
      settings?.ssoSettings || {}
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.updateSsoSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      {},
      { $set: { ssoSettings: req.body } },
      { new: true, upsert: true }
    );
    return httpResponse(
      res,
      200,
      "success",
      "SSO Settings Updated",
      settings.ssoSettings
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.updateGeneralSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      {},
      { $set: { general: req.body } },
      { new: true, upsert: true }
    );
    return httpResponse(
      res,
      200,
      "success",
      "General Settings Updated",
      settings.general
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.updateUserSettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      {},
      { $set: { user: req.body } },
      { new: true, upsert: true }
    );
    return httpResponse(
      res,
      200,
      "success",
      "User Settings Updated",
      settings.user
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.updateSecuritySettings = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      {},
      { $set: { security: req.body } },
      { new: true, upsert: true }
    );
    return httpResponse(
      res,
      200,
      "success",
      "Security Settings Updated",
      settings.security
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.getUserSegmentation = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne(
      {},
      "userSegmentation"
    );
    return httpResponse(
      res,
      200,
      "success",
      "User Segmentation Retrieved",
      settings?.userSegmentation || { segments: [] }
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.createSegment = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne({});
    if (!settings.userSegmentation) {
      settings.userSegmentation = { segments: [] };
    }

    settings.userSegmentation.segments.push(req.body);
    await settings.save();

    return httpResponse(res, 201, "success", "Segment Created", req.body);
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.getSegmentById = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOne(
      {
        "userSegmentation.segments._id": req.params.segmentId,
      },
      {
        "userSegmentation.segments.$": 1,
      }
    );

    if (!settings || !settings.userSegmentation.segments[0]) {
      return httpResponse(res, 404, "error", "Segment not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Segment Retrieved",
      settings.userSegmentation.segments[0]
    );
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.updateSegment = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      { "userSegmentation.segments._id": req.params.segmentId },
      { $set: { "userSegmentation.segments.$": req.body } },
      { new: true }
    );

    if (!settings) {
      console.log("Segment not found", req.params.segmentId);
      return httpResponse(res, 404, "error", "Segment not found");
    }

    return httpResponse(res, 200, "success", "Segment Updated", req.body);
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

exports.deleteSegment = async (req, res) => {
  try {
    const settings = await PlatformSettingsModel.findOneAndUpdate(
      {},
      { $pull: { "userSegmentation.segments": { _id: req.params.segmentId } } },
      { new: true }
    );

    if (!settings) {
      return httpResponse(res, 404, "error", "Segment not found");
    }

    return httpResponse(res, 200, "success", "Segment Deleted");
  } catch (err) {
    return httpResponse(res, 500, "Error", "Server Error", err.message);
  }
};

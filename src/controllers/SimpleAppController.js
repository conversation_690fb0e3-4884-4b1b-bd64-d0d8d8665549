const { default: slugify } = require("slugify");
const UseCaseModel = require("../models/SimpleAppModel");
const { httpResponse } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

exports.addSimpleApp = async (req, res, next) => {
  const {
    title,
    description,
    model,
    prompt,
    simple_app_form,
    temperature,
    top_p,
    presence_penalty,
    frequency_penalty,
    userId,
  } = req.body;

  let slug = slugify(title, { lower: true, strict: true });
  const checkSimpleAppExists = await UseCaseModel.findOne({ slug });

  try {
    if (checkSimpleAppExists) {
      slug = slug + "_" + Math.random().toString(36).substring(2, 15);
    }

    const createNewSimpleApp = await UseCaseModel.create({
      title,
      description,
      slug,
      model,
      prompt,
      simple_app_form,
      temperature,
      top_p,
      presence_penalty,
      frequency_penalty,
      userId,
    });

    return httpResponse(
      res,
      200,
      "success",
      "Simple App Created",
      createNewSimpleApp
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getSimpleApps = async (req, res, next) => {
  const { page = 1, limit = 20, search, userId } = req.query;

  try {
    const query = {};
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { slug: { $regex: search, $options: "i" } },
      ];
    }

    if (userId) {
      query.userId = userId;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const totalUseCases = await UseCaseModel.countDocuments(query);

    const useCases = await UseCaseModel.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalPages = Math.ceil(totalUseCases / parseInt(limit));
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return httpResponse(res, 200, "success", "All Use Cases", {
      useCases,
      pagination: {
        total: totalUseCases,
        page: parseInt(page),
        totalPages,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.editSimpleApp = async (req, res, next) => {
  const { id } = req.params;
  const useCase = req.body;

  try {
    if (useCase.title) {
      const uniqueSlug =
        slugify(useCase.title, { lower: true, strict: true }) +
        "_" +
        Math.random().toString(36).substring(2, 15);
      useCase.slug = uniqueSlug;
    }

    const updatedUseCase = await UseCaseModel.findByIdAndUpdate(
      id,
      { $set: useCase },
      { new: true }
    );

    if (!updatedUseCase) {
      return httpResponse(res, 404, "error", "Use Case not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Use Case updated successfully",
      updatedUseCase
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An error occurred while updating the use case",
      error.message
    );
  }
};

exports.deleteSimpleApp = async (req, res, next) => {
  const { id } = req.params;

  try {
    const deletedUseCase = await UseCaseModel.findByIdAndDelete(id);

    if (!deletedUseCase) {
      return httpResponse(res, 404, "error", "Use Case not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Use Case Deleted",
      deletedUseCase
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getSimpleAppById = async (req, res, next) => {
  const { id } = req.params;

  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(res, 400, "error", "Invalid usecase ID format");
    }

    const useCase = await UseCaseModel.findById(id);
    if (!useCase) {
      return httpResponse(res, 404, "error", "Use Case not found");
    }

    return httpResponse(res, 200, "success", "Use Case retrieved", useCase);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getSimpleAppBySlug = async (req, res, next) => {
  const { slug } = req.params;

  try {
    const useCase = await UseCaseModel.findOne({ slug });

    if (!useCase) {
      return httpResponse(res, 404, "error", "Use Case not found");
    }

    return httpResponse(res, 200, "success", "Use Case retrieved", useCase);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

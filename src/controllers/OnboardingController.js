const UserModel = require("../models/UserModel");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");

exports.getOnboardingById = async (req, res, next) => {
  const userId = req.params.id;
  try {
    const findUser = await UserModel.findById(userId);
    const checkUserOnboardingData = findUser?.onboarding;
    console.log("checkUserOnboardingData:", checkUserOnboardingData);
    if (checkUserOnboardingData === undefined) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Onboarding data not found for this user"
      );
    } else {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Onboarding Data",
        findUser.onboarding
      );
    }
  } catch (err) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Server Error",
      err.message
    );
  }
};

exports.updateOnboarding = async (req, res, next) => {
  const { onboarding } = req.body;
  const userId = req.params.id;
  try {
    const findUser = await UserModel.findByIdAndUpdate(
      userId,
      { onboarding: onboarding },
      {
        new: true,
        upsert: true,
      }
    );
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Onboarding Updated",
      findUser
    );
  } catch (err) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Server Error",
      err.message
    );
  }
};

exports.deleteOnboarding = async (req, res, next) => {
  const userId = req.params.id;
  try {
    await UserModel.findByIdAndUpdate(userId, { onboarding: null });
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Onboarding Deleted",
      null
    );
  } catch (err) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Server Error",
      err.message
    );
  }
};

exports.updateOnboardingIndustry = async (req, res, next) => {
  const { industry } = req.body;
  const userId = req.params.id;
  try {
    const findUser = await UserModel.findByIdAndUpdate(
      userId,
      {
        "onboarding.industry_label": industry,
      },
      { new: true }
    );
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Onboarding Industry Updated",
      findUser
    );
  } catch (err) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Server Error",
      err.message
    );
  }
};

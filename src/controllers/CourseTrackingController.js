const CourseTracking = require("../models/CourseTrackingModel");
const mongoose = require("mongoose");

// Helper function to convert duration string to seconds
const convertDurationToSeconds = (duration) => {
  if (!duration || typeof duration !== "string") return 0;
  try {
    const [minutes, seconds] = duration.split(":").map(Number);
    if (isNaN(minutes) || isNaN(seconds)) return 0;
    return minutes * 60 + seconds;
  } catch (error) {
    console.error("[Progress Debug] Error converting duration:", error);
    return 0;
  }
};

// Kursun tamamlanma durumunu kontrol eden yardımcı fonksiyon
const isCourseCompleted = async (tracking) => {
  try {
    // Eğer chapters yoksa veya boşsa, kurs tamamlanmamıştır
    if (!tracking.chapters || tracking.chapters.length === 0) {
      console.log("[Kurs Kontrolü] Tracking chapters boş, kurs tamamlanmamış.");
      return false;
    }

    try {
      // KRİTİK DEĞİŞİKLİK: <PERSON><PERSON><PERSON> gerçek yapısını MongoDB'den çekelim
      let Course;
      try {
        // Önce varolan modeli almaya çalış
        Course = mongoose.model("Course");
      } catch (modelError) {
        // Model yoksa yeni oluştur
        console.log(
          "[Kurs Kontrolü] Course modeli bulunamadı, oluşturuluyor..."
        );
        const courseSchema = new mongoose.Schema(
          {},
          { strict: false, collection: "courses" }
        );
        Course = mongoose.model("Course", courseSchema);
      }

      // Kurs verilerini veritabanından çek
      const courseData = await Course.findById(tracking.course);

      if (
        !courseData ||
        !courseData.chapters ||
        courseData.chapters.length === 0
      ) {
        console.log(
          "[Kurs Kontrolü] Kurs verisi bulunamadı veya chapters içermiyor, tamamlanmamış kabul edildi."
        );
        return false;
      }

      // Kursun gerçek chapter sayısı ile tracking'deki chapter sayısını karşılaştır
      console.log(
        `[Kurs Kontrolü] Kursta toplam ${courseData.chapters.length} chapter var, tracking'de ${tracking.chapters.length} chapter var.`
      );

      // Eğer kullanıcı kursta bulunan tüm chapterlara başlamamışsa, kurs tamamlanmamıştır
      if (tracking.chapters.length < courseData.chapters.length) {
        console.log(
          `[Kurs Kontrolü] Kullanıcı henüz tüm chapterlara başlamamış (${tracking.chapters.length}/${courseData.chapters.length}), kurs tamamlanmamış.`
        );
        return false;
      }

      // Tüm kurs chapterlarını dolaş ve her birinin tamamlanıp tamamlanmadığını kontrol et
      for (const courseChapter of courseData.chapters) {
        // Bu chapter için tracking kaydı var mı?
        const trackingChapter = tracking.chapters.find(
          (ch) => ch.chapterId.toString() === courseChapter._id.toString()
        );

        // Eğer bu chapter için tracking kaydı yoksa, chapter tamamlanmamıştır
        if (!trackingChapter) {
          console.log(
            `[Kurs Kontrolü] Chapter ${courseChapter._id} için tracking kaydı yok, kurs tamamlanmamış.`
          );
          return false;
        }

        // Eğer chapter tamamlanmamışsa, kurs tamamlanmamıştır
        if (!trackingChapter.completed) {
          console.log(
            `[Kurs Kontrolü] Chapter ${courseChapter._id} tamamlanmamış, kurs tamamlanmamış.`
          );
          return false;
        }

        // Chapter'ın tüm topic'leri kontrol et
        for (const courseTopic of courseChapter.topics) {
          // Bu topic için tracking kaydı var mı?
          const topicTracking = trackingChapter.topics.find(
            (t) => t.topicId.toString() === courseTopic._id.toString()
          );

          // Eğer bu topic için tracking kaydı yoksa veya tamamlanmamışsa, kurs tamamlanmamıştır
          if (!topicTracking) {
            console.log(
              `[Kurs Kontrolü] Topic ${courseTopic._id} için tracking kaydı yok, kurs tamamlanmamış.`
            );
            return false;
          }

          if (!topicTracking.completed) {
            console.log(
              `[Kurs Kontrolü] Topic ${courseTopic._id} tamamlanmamış, kurs tamamlanmamış.`
            );
            return false;
          }
        }
      }

      // Buraya kadar gelebiliyorsak, kursun tüm chapter ve topic'leri tamamlanmış demektir
      console.log(
        `[Kurs Kontrolü] Kursun TÜM chapter ve topic'leri tamamlanmış, kurs TAMAMLANDI.`
      );
      return true;
    } catch (dbError) {
      console.error("[Kurs MongoDB Kontrolü] Hata:", dbError);

      // MongoDB bağlantısı başarısız olursa, daha basit bir kontrolle devam et
      console.log(
        "[Kurs Kontrolü] MongoDB bağlantısı başarısız, basit kontrol yapılıyor..."
      );

      // Bu kapsamlı bir fallback kontrolü olacak
      // 1. "Tamamlanmış chapter sayısı" / "Toplam chapter sayısı" oranını hesaplayalım
      const completedChapters = tracking.chapters.filter(
        (ch) => ch.completed
      ).length;
      const totalChapters = tracking.chapters.length;

      // 2. "Tamamlanmış topic sayısı" / "Toplam topic sayısı" oranını hesaplayalım
      const totalTopics = tracking.chapters.reduce((total, ch) => {
        return total + (ch.topics?.length || 0);
      }, 0);

      const totalCompletedTopics = tracking.chapters.reduce((total, ch) => {
        return (
          total + (ch.topics ? ch.topics.filter((t) => t.completed).length : 0)
        );
      }, 0);

      // Tek topic tamamlandıysa, kesinlikle tamamlanmamış say
      if (totalCompletedTopics <= 2) {
        console.log(
          "[Kurs Kontrolü] Sadece 1-2 topic tamamlanmış, kurs tamamlanmamış kabul edildi."
        );
        return false;
      }

      // 3. Chapter oranı ve topic oranı her ikisi de %95'in üzerindeyse, tamamlanmış kabul et
      const chapterCompletionRatio =
        totalChapters > 0 ? completedChapters / totalChapters : 0;
      const topicCompletionRatio =
        totalTopics > 0 ? totalCompletedTopics / totalTopics : 0;

      // Minimum 5 topic tamamlanmış olması gerekli (kısa kurslar için)
      const isEnoughTopics = totalCompletedTopics >= 5;

      // Ana koşul: Chapter oranı ve topic oranı yüksek VEYA çoklu chapter ve çok sayıda topic tamamlanmış
      const isAlmostComplete =
        chapterCompletionRatio >= 0.95 && topicCompletionRatio >= 0.95;
      const hasMultipleCompletedChapters = completedChapters >= 2;
      const hasManyCompletedTopics = totalCompletedTopics >= 8; // En az 8 topic tamamlanmışsa oldukça iyi bir ilerleme

      const fallbackResult =
        isAlmostComplete ||
        (hasMultipleCompletedChapters &&
          hasManyCompletedTopics &&
          isEnoughTopics);

      console.log(
        `[Kurs Kontrolü] Fallback detaylı kontrol: ` +
          `Chapter tamamlanma oranı: ${(chapterCompletionRatio * 100).toFixed(
            1
          )}%, ` +
          `Topic tamamlanma oranı: ${(topicCompletionRatio * 100).toFixed(
            1
          )}%, ` +
          `Tamamlanan chapter: ${completedChapters}/${totalChapters}, ` +
          `Tamamlanan topic: ${totalCompletedTopics}/${totalTopics}, ` +
          `Kurs tamamlandı mı: ${fallbackResult ? "EVET" : "HAYIR"}`
      );

      return fallbackResult;
    }
  } catch (error) {
    console.error("[Kurs Kontrolü] Genel Hata:", error);
    return false; // Hata durumunda tamamlanmamış olarak kabul et
  }
};

exports.updateCourseProgress = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { chapterId, topicId, completed, progress, isVideoProgress, userId } =
      req.body;

    // Kullanıcının izleme kaydını bul
    let tracking = await CourseTracking.findOne({
      user: userId,
      course: courseId,
    });

    console.log(
      `[İlerleme Güncellemesi Başladı] UserID: ${userId}, CourseID: ${courseId}, ChapterID: ${chapterId}, TopicID: ${topicId}, Completed: ${completed}`
    );

    // İzleme kaydı yoksa oluştur
    if (!tracking) {
      tracking = await CourseTracking.create({
        user: userId,
        course: courseId,
        courseComplateStatus: "progress",
        chapters: [],
        startedAt: new Date(),
        lastAccessed: new Date(),
      });
      console.log(
        `[Yeni İzleme Kaydı] UserID: ${userId} için yeni izleme kaydı oluşturuldu.`
      );
    }

    // İlgili chapter'ı bul veya oluştur
    let chapter = tracking.chapters.find(
      (ch) => ch.chapterId.toString() === chapterId
    );
    if (!chapter) {
      tracking.chapters.push({
        chapterId,
        completed: false,
        topics: [],
        startedAt: new Date(),
        lastAccessed: new Date(),
      });
      chapter = tracking.chapters[tracking.chapters.length - 1];
      console.log(`[Yeni Chapter] ChapterID: ${chapterId} tracking'e eklendi.`);
    }

    // İlgili topic'i bul veya oluştur
    let topic = chapter.topics.find((t) => t.topicId.toString() === topicId);
    if (!topic) {
      chapter.topics.push({
        topicId,
        completed: false,
        progress: 0,
        startedAt: new Date(),
        lastAccessed: new Date(),
      });
      topic = chapter.topics[chapter.topics.length - 1];
      console.log(`[Yeni Topic] TopicID: ${topicId} chapter'a eklendi.`);
    }

    // Progress ve completed durumunu güncelle
    const previousCompleted = topic.completed;

    if (isVideoProgress) {
      // Video ilerleme durumu
      topic.progress = progress;

      // Video progress 1 (100%) ise ve açıkça belirtilmişse topic'i tamamlandı olarak işaretle
      if (progress >= 1 && completed === true) {
        topic.completed = true;
        topic.completedAt = new Date();
      }
      // Eğer açıkça completed: false gönderilmişse, progress ne olursa olsun tamamlanmamış olarak kal
      else if (completed === false) {
        topic.completed = false;
      }
    } else {
      // Video dışı içerik (quiz, form vb.)
      // Sadece açıkça completed: true gönderilmişse tamamlanmış olarak işaretle
      if (completed === true) {
        topic.completed = true;
        topic.completedAt = new Date();
        topic.progress = 1;
      } else if (completed === false) {
        // Açıkça completed: false gönderilmişse ve progress değeri varsa, sadece progress'i güncelle
        if (progress !== undefined) {
          topic.progress = progress;
        }
        topic.completed = false;
      }
    }

    if (previousCompleted !== topic.completed) {
      console.log(
        `[Topic Durumu Değişti] TopicID: ${topicId} durumu: ${
          previousCompleted ? "tamamlanmış" : "tamamlanmamış"
        } -> ${topic.completed ? "tamamlanmış" : "tamamlanmamış"}`
      );
    }

    // Chapter completion check - eğer tüm konular tamamlanmışsa chapter'ı tamamlanmış olarak işaretle
    // Konular dizisi boş değilse ve her bir konu tamamlanmışsa chapter tamamlanmış demektir
    const previousChapterCompleted = chapter.completed;
    if (chapter.topics.length > 0) {
      const allTopicsCompleted = chapter.topics.every((t) => t.completed);
      chapter.completed = allTopicsCompleted;
      if (allTopicsCompleted && !chapter.completedAt) {
        chapter.completedAt = new Date();
      }

      if (previousChapterCompleted !== chapter.completed) {
        console.log(
          `[Chapter Durumu Değişti] ChapterID: ${chapterId} durumu: ${
            previousChapterCompleted ? "tamamlanmış" : "tamamlanmamış"
          } -> ${chapter.completed ? "tamamlanmış" : "tamamlanmamış"}`
        );
      }
    } else {
      chapter.completed = false;
    }

    // Kursun tamamlanma durumunu kontrol et ve güncelle
    const previousStatus = tracking.courseComplateStatus;

    // Kurs tamamlanma kontrolü - artık async
    const isCompleted = await isCourseCompleted(tracking);

    if (isCompleted) {
      tracking.courseComplateStatus = "completed";
      if (!tracking.completedAt) {
        tracking.completedAt = new Date();
      }
    } else {
      tracking.courseComplateStatus = "progress";
      // Kurs tamamlanmadı olarak işaretlendiğinde, tamamlanma tarihini kaldır
      tracking.completedAt = undefined;
    }

    // Durum değişikliğini logla
    if (previousStatus !== tracking.courseComplateStatus) {
      console.log(
        `[Kurs Durum Değişikliği] Önceki: '${previousStatus}', ` +
          `Yeni: '${tracking.courseComplateStatus}', ` +
          `User: ${userId}, Course: ${courseId}`
      );
    }

    // Update timestamps
    topic.lastAccessed = new Date();
    chapter.lastAccessed = new Date();
    tracking.lastAccessed = new Date();

    // Veritabanını güncelle
    await tracking.save();
    console.log(
      `[İlerleme Güncelleme Tamamlandı] UserID: ${userId}, CourseID: ${courseId}`
    );

    res.status(200).json({
      status: "success",
      data: tracking,
    });
  } catch (error) {
    console.error("Kurs ilerleme güncelleme hatası:", error);
    res.status(400).json({
      status: "error",
      message: error.message,
    });
  }
};

exports.getCourseProgress = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { userId } = req.query;

    let tracking = await CourseTracking.findOne({
      user: userId,
      course: courseId,
    }).populate("user", "name email");

    if (!tracking) {
      // Eğer kayıt yoksa yeni bir kayıt oluştur
      tracking = await CourseTracking.create({
        user: userId,
        course: courseId,
        courseComplateStatus: "progress",
        chapters: [],
        startedAt: new Date(),
        lastAccessed: new Date(),
      });

      // Yeni oluşturulan kaydı populate et
      tracking = await tracking.populate("user", "name email");
    }

    res.status(200).json({
      status: "success",
      data: tracking,
    });
  } catch (error) {
    res.status(400).json({
      status: "error",
      message: error.message,
    });
  }
};

exports.getUserCoursesProgress = async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.query;

    // Base query
    const query = {
      user: userId,
      // Filter out courses with empty chapters
      chapters: {
        $exists: true,
        $not: { $size: 0 },
      },
    };

    // Add status filter if provided
    if (status) {
      query.courseComplateStatus = status;
    }

    // Add complex filtering for single uncompleted chapter/topic
    query.$nor = [
      {
        $and: [
          // Must have exactly 1 chapter
          { "chapters.1": { $exists: false } },
          { "chapters.0": { $exists: true } },
          // That chapter must have exactly 1 topic
          { "chapters.0.topics.1": { $exists: false } },
          { "chapters.0.topics.0": { $exists: true } },
          // And that topic must be uncompleted
          { "chapters.0.topics.0.completed": false },
        ],
      },
    ];

    const courses = await CourseTracking.find(query).lean().exec();

    // Additional validation to ensure we're not missing any edge cases
    const validCourses = courses.filter((course) => {
      // Skip courses that have single chapter with single uncompleted topic
      if (
        course.chapters.length === 1 &&
        course.chapters[0].topics.length === 1 &&
        !course.chapters[0].topics[0].completed
      ) {
        return false;
      }
      return true;
    });

    return res.status(200).json({
      status: "success",
      count: validCourses.length,
      data: validCourses,
    });
  } catch (error) {
    console.error("Error fetching course progress:", {
      userId: req.params.userId,
      error: error.message,
      stack: error.stack,
    });

    return res.status(400).json({
      status: "error",
      message: error.message,
    });
  }
};

// Kursu manuel olarak tamamlandı olarak işaretleyen endpoint
exports.completeCourse = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { userId } = req.body;

    if (!courseId || !userId) {
      return res.status(400).json({
        status: "error",
        message: "Course ID and User ID are required",
      });
    }

    console.log(
      `[Manuel Course Completion] UserID: ${userId}, CourseID: ${courseId}`
    );

    // Find the user's course tracking record
    let tracking = await CourseTracking.findOne({
      user: userId,
      course: courseId,
    });

    // If no tracking record exists, create a new one
    if (!tracking) {
      tracking = await CourseTracking.create({
        user: userId,
        course: courseId,
        courseComplateStatus: "completed", // Doğrudan "completed" olarak işaretle
        chapters: [],
        startedAt: new Date(),
        lastAccessed: new Date(),
        completedAt: new Date(),
      });
      console.log(
        `[Yeni Tamamlanmış Kayıt] UserID: ${userId} için yeni tamamlanmış izleme kaydı oluşturuldu.`
      );
    } else {
      // Varolan kaydı güncelle
      const previousStatus = tracking.courseComplateStatus;
      tracking.courseComplateStatus = "completed";
      tracking.lastAccessed = new Date();

      // Eğer daha önce tamamlanma tarihi ayarlanmamışsa ayarla
      if (!tracking.completedAt) {
        tracking.completedAt = new Date();
      }

      await tracking.save();

      // Durum değişikliğini logla
      if (previousStatus !== "completed") {
        console.log(
          `[Manuel Kurs Tamamlama] Önceki durum: '${previousStatus}', ` +
            `Yeni durum: 'completed', ` +
            `User: ${userId}, Course: ${courseId}`
        );
      }
    }

    res.status(200).json({
      status: "success",
      message: "Kurs başarıyla tamamlandı olarak işaretlendi",
      data: tracking,
    });
  } catch (error) {
    console.error("Kurs tamamlama hatası:", error);
    res.status(400).json({
      status: "error",
      message: error.message,
    });
  }
};

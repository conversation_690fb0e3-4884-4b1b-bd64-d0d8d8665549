const JourneyTrackingModel =
  require("../../models/TrackingModel").JourneyTrackingModel;
const AppTrackingModel = require("../../models/AppTrackingModel");
const SimpleAppModel = require("../../models/SimpleAppModel");
const WorkflowModel = require("../../models/WorkflowModel");
const Certificate = require("../../models/CertificateModel");
const CourseTrackingModel = require("../../models/CourseTrackingModel");
const UserModel = require("../../models/UserModel");
const FormResponseModel = require("../../models/FormResponse");
const mongoose = require("mongoose");
const ReportModel = require("../../models/ReportModel");
exports.generateNextgenTalentReport = async ({
  companyId,
  startDay,
  startMonth,
  startYear,
  endDay,
  endMonth,
  endYear,
}) => {
  try {
    // Şirket ID'si geçerli mi kontrol et
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Nextgen Talent",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Şirkete ait kullanıcıları bul
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    });

    if (!users || users.length === 0) {
      return {
        reportType: "Nextgen Talent",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Her kullanıcı için istatistikleri topla
    const userStats = [];

    for (const user of users) {
      const userId = user._id;

      // Usecase kullanım istatistikleri
      const usecaseFilter = { userId: userId };
      const docs = await AppTrackingModel.find(usecaseFilter).sort({
        year: -1,
        month: -1,
        day: -1,
        appId: 1,
      });
      const totalUsage = docs.reduce((acc, doc) => acc + doc.count, 0);
      const totalUniqueUsecase = await AppTrackingModel.distinct(
        "appId",
        usecaseFilter
      );

      // Simple App kullanım istatistikleri
      const simpleAppFilter = { userId: userId };
      const simpleApps = await SimpleAppModel.find(simpleAppFilter);
      const totalSimpleApp = simpleApps.length;

      // Workflow kullanım istatistikleri
      const workflowFilter = { userId: userId };
      const workflows = await WorkflowModel.find(workflowFilter);
      const totalWorkflow = workflows.length;

      // Journey kullanım istatistikleri
      const journey = await JourneyTrackingModel.findById(userId);
      let totalJourneyItems = 0;
      let completedJourneys = 0;
      if (journey) {
        if (journey.beginner && Array.isArray(journey.beginner)) {
          totalJourneyItems += journey.beginner.length;
        } else if (journey.beginner && typeof journey.beginner === "object") {
          totalJourneyItems += Object.keys(journey.beginner).length;
        }
        if (journey.expert && Array.isArray(journey.expert)) {
          totalJourneyItems += journey.expert.length;
          if (journey.expert.length > 0) {
            completedJourneys = 1;
          }
        } else if (journey.expert && typeof journey.expert === "object") {
          totalJourneyItems += Object.keys(journey.expert).length;
        }

        if (journey.master && Array.isArray(journey.master)) {
          totalJourneyItems += journey.master.length;
          if (journey.master.length > 0) {
            completedJourneys = 2;
          }
        } else if (journey.master && typeof journey.master === "object") {
          totalJourneyItems += Object.keys(journey.master).length;
        }
      }
      // Check if user has completed all journeys
      if (user?.allJourneyCompleted === true) {
        completedJourneys = 3;
      }
      // Sertifika istatistikleri
      let totalCertificates = await Certificate.countDocuments({
        userId: userId,
      });

      // Fikir istatistikleri
      let totalIdeas = 0;
      const getSubmittedIdeas = await FormResponseModel.find({
        submittedBy: userId,
        formType: "ideation",
      });
      totalIdeas = getSubmittedIdeas.length;

      // Tamamlanan kurs istatistikleri
      let getUserCompletedCourses = await CourseTrackingModel.countDocuments({
        user: userId,
        courseComplateStatus: "completed",
      });

      // Kullanıcı istatistiklerini ekle
      userStats.push({
        userId: userId,
        name: user.name,
        surname: user.surname,
        email: user.email,
        totalUsecaseUsage: totalUsage,
        totalUniqueUsecase: totalUniqueUsecase.length,
        totalSimpleApp: totalSimpleApp,
        totalWorkflow: totalWorkflow,
        totalAISolutions: totalSimpleApp + totalWorkflow,
        totalJourneySteps: totalJourneyItems,
        totalJourneyCompleted: completedJourneys,
        totalCertificates: totalCertificates,
        totalIdeas: totalIdeas,
        getUserCompletedCourses: getUserCompletedCourses,
        totalAIScore:
          totalUsage +
          totalUniqueUsecase.length * 2 +
          (totalSimpleApp + totalWorkflow) * 15 +
          completedJourneys * 10 +
          totalCertificates * 5 +
          totalIdeas * 10,
      });
    }

    // totalAIScore'a göre sırala (büyükten küçüğe)
    userStats.sort((a, b) => b.totalAIScore - a.totalAIScore);
    // add inside report model
    const report = await ReportModel.create({
      company: companyId,
      reportType: "nextgen",
      downloadLink: "not available",
      reportData: userStats,
      createdAt: new Date(),
    });
    return {
      company: companyId,
      reportType: "Nextgen Talent",
      status: "success",
      downloadLink: "not available",
      message: "User Stats",
      data: userStats,
    };
  } catch (err) {
    console.error("Error fetching user stats:", err);
    return {
      reportType: "Nextgen Talent",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};

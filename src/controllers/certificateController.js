const fs = require("fs");
const path = require("path");
const { BlobServiceClient } = require("@azure/storage-blob");
const { httpResponse } = require("../utils/helpers");
const ENUM = require("../utils/enum");
const Certificate = require("../models/CertificateModel");
const generateCertificate = require("../utils/certificateGenerator");
const generateQRCode = require("../utils/qrGenerator");
const config = require("../../config");

// Azure Storage Client oluştur
const blobServiceClient = BlobServiceClient.fromConnectionString(
  process.env.AZURE_STORAGE_CONNECTION_STRING
);
const containerName = "certificates";

exports.generateCertificate = async (req, res) => {
  try {
    const {
      userId,
      courseId,
      fullName,
      certificateName,
      issueDate,
      organizationName,
      instructorName,
      completionTime,
    } = req.body;

    if (!fullName || !certificateName || !userId || !courseId) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Full Name, Certificate Name, User ID and Course ID are required"
      );
    }

    // Mevcut sertifika kontrolü
    const existingCertificate = await Certificate.findOne({
      userId,
      courseId,
      status: "active",
    });

    if (existingCertificate) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Certificate already exists",
        {
          certificateNumber: existingCertificate.certificateNumber,
          securityCode: existingCertificate.securityCode,
          fileName: existingCertificate.fileName,
          downloadUrl: existingCertificate.downloadUrl,
        }
      );
    }

    const certificateNumber = `CERT-${Date.now()}`;
    const securityCode = Math.random()
      .toString(36)
      .substring(2, 8)
      .toUpperCase();

    // Generate QR Code
    const qrCode = await generateQRCode(
      certificateNumber,
      securityCode,
      config.baseUrl
    );

    // Prepare certificate data
    const certificateData = {
      userId,
      courseId,
      fullName,
      certificateName,
      issueDate: issueDate,
      organizationName: organizationName,
      instructorName: instructorName,
      completionTime: completionTime,
      certificateNumber,
      securityCode,
      qrCode,
    };

    // Generate certificate PDF
    const pdfBuffer = await generateCertificate(certificateData);

    // Generate file name
    const fileName = `${fullName
      .replace(/[<>:"/\\|?*]/g, "") // Sadece dosya sistemi için geçersiz karakterleri temizle
      .replace(/\s+/g, "_")}_${certificateNumber}.pdf`;

    // Azure Blob Storage'a yükle
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(fileName);

    await blockBlobClient.upload(pdfBuffer, pdfBuffer.length, {
      blobHTTPHeaders: { blobContentType: "application/pdf" },
    });

    // Azure Blob URL'ini oluştur
    const blobUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${fileName}`;

    // Create certificate record in database
    await Certificate.create({
      ...certificateData,
      fileName,
      downloadUrl: blobUrl,
      status: "active",
    });

    return httpResponse(
      res,
      ENUM.HTTP_CODES.CREATED,
      "success",
      "Certificate generated successfully",
      {
        certificateNumber,
        securityCode,
        fileName,
        downloadUrl: blobUrl,
      }
    );
  } catch (error) {
    console.error("Certificate generation error:", error);
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.downloadCertificate = async (req, res) => {
  try {
    const { fileName } = req.params;

    // Sertifikayı veritabanından bul
    const certificate = await Certificate.findOne({
      fileName,
      status: "active",
    });

    if (!certificate) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Certificate not found"
      );
    }

    // Azure Blob'dan sertifikayı indir
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(fileName);

    // Blob'un varlığını kontrol et
    const exists = await blockBlobClient.exists();
    if (!exists) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Certificate file not found in storage"
      );
    }

    // PDF'i stream olarak gönder
    const blobDownloadResponse = await blockBlobClient.download();
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `inline; filename="${fileName}"`);

    blobDownloadResponse.readableStreamBody.pipe(res);
  } catch (error) {
    console.error("Certificate download error:", error);
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.verifyCertificate = async (req, res) => {
  try {
    const { cn: certificateNumber, sc: securityCode } = req.query;

    if (!certificateNumber || !securityCode) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Certificate number and security code are required"
      );
    }

    const certificate = await Certificate.findOne({
      certificateNumber,
      securityCode,
      status: "active",
    });

    if (!certificate) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Invalid or revoked certificate"
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Certificate verified successfully",
      {
        fullName: certificate.fullName,
        certificateName: certificate.certificateName,
        issueDate: certificate.issueDate,
        organizationName: certificate.organizationName,
        status: certificate.status,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.listCertificates = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, userId } = req.query;
   
    const query = status ? { status } : {};

    if (userId) {
      query.userId = userId;
    }

    const certificates = await Certificate.find(query)
      .select("-securityCode") // Exclude sensitive data
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Certificate.countDocuments(query);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Certificates retrieved successfully",
      {
        certificates,
        total,
        page: parseInt(page),
        totalPages: Math.ceil(total / limit),
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.revokeCertificate = async (req, res) => {
  try {
    const { certificateNumber } = req.params;
    const { reason } = req.body;

    const certificate = await Certificate.findOne({ certificateNumber });

    if (!certificate) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Certificate not found"
      );
    }

    certificate.status = "revoked";
    certificate.metaData.set("revocationReason", reason);
    certificate.metaData.set("revokedAt", new Date().toISOString());

    await certificate.save();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Certificate revoked successfully"
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.checkCertificate = async (req, res) => {
  try {
    const { userId, courseId } = req.query;

    if (!userId || !courseId) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "User ID and Course ID are required"
      );
    }

    const certificate = await Certificate.findOne({
      userId,
      courseId,
      status: "active",
    });
 

    if (!certificate) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "Certificate not found"
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Certificate found",
      {
        certificate,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

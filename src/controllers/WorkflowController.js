const { default: slugify } = require("slugify");
const WorkflowModel = require("../models/WorkflowModel");
const { httpResponse } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

exports.addWorkflow = async (req, res, next) => {
  const {
    title,
    workflow_form,
    temperature,
    top_p,
    presence_penalty,
    frequency_penalty, 
    userId
  } = req.body;

  try {
    
    let slug = slugify(title, { lower: true, strict: true });
    const checkWorkflowExists = await WorkflowModel.findOne({ slug });

    if (checkWorkflowExists) {
      slug = slug + "_" + Math.random().toString(36).substring(2, 15);
    } 

      const createNewWorkflow = await WorkflowModel.create({
        title,
        slug,
        workflow_form,
        temperature,
        top_p,
        presence_penalty,
        frequency_penalty, 
        userId
      });

      return httpResponse(
        res,
        200,
        "success",
        "Workflow Created",
        createNewWorkflow
      ); 
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getWorkflows = async (req, res, next) => {
  const { page = 1, limit = 20, search, userId } = req.query;

  try {
    const query = {};
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { slug: { $regex: search, $options: "i" } },
      ];
    }
    if (userId) {
      query.userId = userId;
    }
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const totalWorkflows = await WorkflowModel.countDocuments(query);

    const workflows = await WorkflowModel.find(query)  
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    if (workflows.length === 0 && userId) {
      return httpResponse(res, 200, "success", "No workflows found for this user", []);
    }

    const totalPages = Math.ceil(totalWorkflows / parseInt(limit));
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return httpResponse(res, 200, "success", "All Workflows", {
      workflows,
      pagination: {
        total: totalWorkflows,
        page: parseInt(page),
        totalPages,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getWorkflowBySlug = async (req, res, next) => {
  const { slug } = req.params;

  try {
    const workflow = await WorkflowModel.findOne({ slug });

    if (!workflow) {
      return httpResponse(res, 404, "error", "Workflow not found");
    }

    return httpResponse(res, 200, "success", "Workflow retrieved", workflow);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.getWorkflowById = async (req, res, next) => {
  const { id } = req.params;

  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(res, 400, "error", "Invalid workflow ID format");
    }

    const workflow = await WorkflowModel.findById(id);

    if (!workflow) {
      return httpResponse(res, 404, "error", "Workflow not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Single Workflow retrieved",
      workflow
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
}; 

exports.editWorkflow = async (req, res, next) => {
  const { id } = req.params;
  const updates = req.body;

  try {
    if (updates.title) {
      
    }

    const updatedWorkflow = await WorkflowModel.findOneAndUpdate(
      { _id: id },
      updates,
      { new: true }
    );

    if (!updatedWorkflow) {
      return httpResponse(res, 404, "error", "Workflow not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Workflow updated successfully",
      updatedWorkflow
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

exports.deleteWorkflow = async (req, res, next) => {
  const { id } = req.params;

  try {
    const deletedWorkflow = await WorkflowModel.findOneAndDelete({ _id: id });

    if (!deletedWorkflow) {
      return httpResponse(res, 404, "error", "Workflow not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Workflow deleted successfully",
      deletedWorkflow
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
 
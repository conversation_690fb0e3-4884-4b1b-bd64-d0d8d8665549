const ReportModel = require("../models/ReportModel");
const { httpResponse } = require("../utils/helpers");
const path = require("path");
const fs = require("fs");

exports.getReports = async (req, res, next) => {
  const {
    company,
    reportID,
    reportType,
    reportName,
    platformName,
    platformUrl,
    downloadLink,
    createdAt,
  } = req.body;
  try {
    const findReport = await ReportModel.create({
      company,
      reportID,
      reportName,
      reportType,
      platformName,
      platformUrl,
      downloadLink,
      createdAt,
    });
    return httpResponse(
      res,
      200,
      "Success",
      "Report created successfull",
      findReport
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.readReports = async (req, res, next) => {
  const { reportType, platformName } = req.body;
  try {
    const findReports = await ReportModel.find({ reportType, platformName });
    return httpResponse(
      res,
      200,
      "success",
      `${platformName} Platform reports`,
      findReports
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
exports.downloadDummyReport = async (req, res, next) => {
  const { reportType } = req.body;
  try {
    const filePath = path.join(
      __dirname,
      "../assets/reports",
      `${reportType}.xlsx`
    );

    if (fs.existsSync(filePath)) {
      res.download(filePath, `${reportType}.xlsx`, (err) => {
        if (err) {
          return httpResponse(
            res,
            500,
            "error",
            "An error occurred while downloading the file.",
            err.message
          );
        }
      });
    } else {
      return httpResponse(
        res,
        404,
        "error",
        "The requested report was not found."
      );
    }
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

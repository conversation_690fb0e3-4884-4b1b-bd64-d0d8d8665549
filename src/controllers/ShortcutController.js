const ShortcutsModel = require("../models/ShortcutsModel");
const UserModel = require("../models/UserModel");
const SimpleAppModel = require("../models/SimpleAppModel");
const { httpResponse } = require("../utils/helpers");
const mongoose = require("mongoose");
const axios = require("axios");

const updateShortcut = async (req, res) => {
  try {
    const { userId, shortcutType, shortcutUrl, shortcutID } = req.body;

    // Validation checks
    if (!userId || !shortcutType) {
      return httpResponse(res, 400, "error", "Missing required fields");
    }

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return httpResponse(res, 400, "error", "Invalid userId format");
    }

    // Convert userId to ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // Check if user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      return httpResponse(res, 404, "error", "User not found");
    }

    // Validate shortcut type
    const validShortcutTypes = [
      "page",
      "usecase",
      "course",
      "workflow",
      "playground",
      "prompt",
      "ai_app",
    ];
    if (!validShortcutTypes.includes(shortcutType)) {
      return httpResponse(res, 400, "error", "Invalid shortcut type");
    }

    // Validate shortcutUrl for page type
    if (shortcutType === "page" && !shortcutUrl) {
      return httpResponse(
        res,
        400,
        "error",
        "shortcutUrl is required for page type shortcuts"
      );
    }

    // Validate shortcutID for non-page types
    if (shortcutType !== "page" && !shortcutID) {
      return httpResponse(
        res,
        400,
        "error",
        "shortcutID is required for non-page type shortcuts"
      );
    }

    if (
      shortcutType !== "page" &&
      !mongoose.Types.ObjectId.isValid(shortcutID)
    ) {
      return httpResponse(res, 400, "error", "Invalid shortcutID format");
    }

    // Build filter object
    const filter = {
      userId: userObjectId,
      shortcutType,
    };

    // Add appropriate identifier based on shortcut type
    if (shortcutType === "page") {
      filter.shortcutUrl = shortcutUrl;
    } else {
      filter.shortcutID = new mongoose.Types.ObjectId(shortcutID);
    }

    // Update document
    const updateDoc = {
      $set: {
        userId: userObjectId,
        shortcutType,
        ...(shortcutType === "page" ? { shortcutUrl } : { shortcutID }),
        createdAt: new Date(), // MongoDB will handle this only on insert due to upsert
      },
    };

    const options = { upsert: true, new: true };

    const result = await ShortcutsModel.findOneAndUpdate(
      filter,
      updateDoc,
      options
    );

    return httpResponse(
      res,
      200,
      "success",
      "Shortcut updated successfully",
      result
    );
  } catch (err) {
    if (err.code === 11000) {
      // Duplicate key error
      return httpResponse(res, 409, "error", "Shortcut already exists");
    }
    console.error("Error updating shortcut:", err);
    return httpResponse(
      res,
      500,
      "error",
      "Internal Server Error",
      err.message
    );
  }
};

const getShortcuts = async (req, res) => {
  try {
    const { userId, shortcutType } = req.query;
    const filter = {};

    // Add filters if provided
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return httpResponse(res, 400, "error", "Invalid userId format");
      }
      filter.userId = new mongoose.Types.ObjectId(userId);
    }

    if (shortcutType) {
      const validShortcutTypes = [
        "page",
        "usecase",
        "course",
        "workflow",
        "playground",
        "prompt",
        "ai_app",
      ];
      if (!validShortcutTypes.includes(shortcutType)) {
        return httpResponse(res, 400, "error", "Invalid shortcut type");
      }
      filter.shortcutType = shortcutType;
    }

    // Get shortcuts
    const shortcuts = await ShortcutsModel.find(filter).sort({ createdAt: -1 });

    // shortcutType filtresi olduğunda ya da olmadığında da API'den veri çekilecek
    if (shortcuts.length > 0) {
      try {
        const enrichedShortcuts = await Promise.all(
          shortcuts?.map(async (shortcut) => {
            try {
              // shortcutID varsa, API'den veri çek
              if (shortcut && shortcut?.shortcutID) {
                const id = shortcut.shortcutID;
                const currentShortcutType = shortcut.shortcutType; // Her shortcut'ın kendi türünü kullanıyoruz

                // shortcutType'a göre dinamik URL oluştur
                let apiUrl;
                let headers = {
                  Accept: "application/json",
                  "Content-Type": "application/json",
                };

                switch (currentShortcutType) {
                  case "prompt":
                    apiUrl = `${process.env.CDS_URL}/promptlibrary/${id}`;
                    headers["x-api-key"] = process.env.CDS_API_KEY;
                    break;
                  case "usecase":
                    apiUrl = `${process.env.CDS_URL}/usecase/${id}`;
                    headers["x-api-key"] = process.env.CDS_API_KEY;
                    break;
                  case "workflow":
                    apiUrl = `${process.env.CDS_URL}/workflow/${id}`;
                    headers["x-api-key"] = process.env.CDS_API_KEY;
                    break;
                  case "ai_app":
                    // API çağrısı yapmak yerine veritabanından veri çekiyoruz
                    const appData = await SimpleAppModel.findById(id);
                    if (!appData) {
                      throw new Error(`Simple App with ID ${id} not found`);
                    }
                    
                    // Shortcut verisini veritabanı yanıtıyla birleştir
                    const shortcutObj = shortcut.toObject();
                    return {
                      ...shortcutObj,
                      apiData: appData, // Veritabanından gelen veriyi apiData olarak ekle
                    };
                  case "page":
                    // Page türü için url bilgisini apiData olarak ekle
                    const pageObj = shortcut.toObject();
                    return {
                      ...pageObj,
                      apiData: { url: shortcut.shortcutUrl } // Page için url bilgisini apiData olarak ekle
                    };
                  //   case "course":
                  //     apiUrl = `${process.env.CDS_URL}/api/v1/course/${id}`;
                  //     break;
                  //   case "playground":
                  //     apiUrl = `${process.env.CDS_URL}/api/v1/playground/${id}`;
                  //     break;
                  default:
                    // Eğer desteklenmeyen bir shortcutType ise, API isteği yapmadan devam et
                    return shortcut.toObject();
                }

                // Eğer ai_app veya page değilse API isteği yapıyoruz
                if (currentShortcutType !== "ai_app" && currentShortcutType !== "page") {
                  // API isteği yap
                  const apiResponse = await axios.get(apiUrl, {
                    timeout: 5000, // 5 saniye timeout
                    headers: headers,
                  });

                  // Shortcut verisini API yanıtıyla birleştir
                  const shortcutObj = shortcut.toObject();

                  // API yanıtını shortcut nesnesine ekle
                  return {
                    ...shortcutObj,
                    apiData: apiResponse.data.data, // API yanıtını apiData alanı altında ekle
                  };
                }
              }
              return shortcut.toObject();
            } catch (error) {
              console.error(
                `Error fetching data for ${shortcut.shortcutType} ${shortcut._id}:`,
                error.message
              );
              // API hatası durumunda orijinal shortcut'ı döndür, hata bilgisiyle birlikte
              return {
                ...shortcut.toObject(),
                apiError: `Failed to fetch ${shortcut.shortcutType} data: ${error.message}`,
              };
            }
          })
        );

        return httpResponse(
          res,
          200,
          "success",
          `Shortcuts retrieved successfully with data`,
          enrichedShortcuts
        );
      } catch (error) {
        console.error(
          `Error enriching shortcuts with API data:`,
          error
        );
        // API işlemi sırasında genel bir hata oluşursa, orijinal shortcuts'ları döndür
        return httpResponse(
          res,
          200,
          "success",
          "Shortcuts retrieved successfully (without API enrichment due to error)",
          shortcuts
        );
      }
    }

    return httpResponse(
      res,
      200,
      "success",
      "Shortcuts retrieved successfully",
      shortcuts
    );
  } catch (err) {
    console.error("Error fetching shortcuts:", err);
    return httpResponse(
      res,
      500,
      "error",
      "Internal Server Error",
      err.message
    );
  }
};

const deleteShortcut = async (req, res) => {
  try {
    const { shortcutId, userId } = req.params;

    // Parametre kontrolü
    if (!shortcutId || !userId) {
      return httpResponse(
        res,
        400,
        "error",
        "Shortcut ID and User ID are required"
      );
    }

    // ID formatı kontrolü
    if (
      !mongoose.Types.ObjectId.isValid(shortcutId) ||
      !mongoose.Types.ObjectId.isValid(userId)
    ) {
      return httpResponse(res, 400, "error", "Invalid ID format");
    }

    // Önce shortcut'ı bul
    const shortcut = await ShortcutsModel.findById(shortcutId);

    if (!shortcut) {
      return httpResponse(res, 404, "error", "Shortcut not found");
    }

    // Shortcut'ın sahibi URL'deki kullanıcı mı kontrol et
    if (!shortcut.userId.equals(userId)) {
      return httpResponse(
        res,
        403,
        "error",
        "This shortcut doesn't belong to the specified user"
      );
    }

    // Shortcut'ı sil
    await ShortcutsModel.findByIdAndDelete(shortcutId);

    return httpResponse(res, 200, "success", "Shortcut deleted successfully");
  } catch (err) {
    console.error("Error deleting shortcut:", err);
    return httpResponse(
      res,
      500,
      "error",
      "Internal Server Error",
      err.message
    );
  }
};

module.exports = { updateShortcut, getShortcuts, deleteShortcut };

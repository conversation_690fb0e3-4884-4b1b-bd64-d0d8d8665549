const { default: slugify } = require("slugify");
const AppTrackingModel = require("../models/AppTrackingModel");
const { JourneyTrackingModel } = require("../models/TrackingModel");
const SimpleAppModel = require("../models/SimpleAppModel");
const WorkflowModel = require("../models/WorkflowModel");
const Certificate = require("../models/CertificateModel");
const CourseTrackingModel = require("../models/CourseTrackingModel");
const UserModel = require("../models/UserModel");
const FormResponseModel = require("../models/FormResponse");
const { httpResponse } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

const getStats = async (req, res) => {
  try {
    const { userId } = req.params;
    const usecaseFilter = {};

    /**
     * Get Usecase Usage
     */
    // Check if userId is a valid ObjectId
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: "Invalid userId format" });
      }
      usecaseFilter.userId = new mongoose.Types.ObjectId(userId);
    }
    usecaseFilter.appType = "usecase";
    // If userId is provided, use the original logic
    const docs = await AppTrackingModel.find(usecaseFilter).sort({
      year: -1,
      month: -1,
      day: -1,
      appId: 1,
    });
    const totalUsage = docs.reduce((acc, doc) => acc + doc.count, 0);
    const totalUniqueUsecase = await AppTrackingModel.distinct(
      "appId",
      usecaseFilter
    );

    /**
     * Get Simple App Usage
     */
    const simpleAppFilter = {};
    simpleAppFilter.userId = userId;
    const simpleApps = await SimpleAppModel.find(simpleAppFilter);
    const totalSimpleApp = simpleApps.length;

    /**
     * Get Workflow Usage
     */
    const workflowFilter = {};
    workflowFilter.userId = userId;
    const workflows = await WorkflowModel.find(workflowFilter);
    const totalWorkflow = workflows.length;

    /**
     * Get Journey Usage
     */
    const journey = await JourneyTrackingModel.findById(userId);

    let totalJourneyItems = 0;

    if (journey.beginner && Array.isArray(journey.beginner)) {
      totalJourneyItems += journey.beginner.length;
    } else if (journey.beginner && typeof journey.beginner === "object") {
      totalJourneyItems += Object.keys(journey.beginner).length;
    }

    if (journey.expert && Array.isArray(journey.expert)) {
      totalJourneyItems += journey.expert.length;
    } else if (journey.expert && typeof journey.expert === "object") {
      totalJourneyItems += Object.keys(journey.expert).length;
    }

    if (journey.master && Array.isArray(journey.master)) {
      totalJourneyItems += journey.master.length;
    } else if (journey.master && typeof journey.master === "object") {
      totalJourneyItems += Object.keys(journey.master).length;
    }

    let totalCertificates = await Certificate.countDocuments({
      userId: userId,
    });

    let totalIdeas = 0;
    const getSubmittedIdeas = await FormResponseModel.find({
      submittedBy: userId,
      formType: "ideation",
    });
    totalIdeas = getSubmittedIdeas.length;
    let getUserCompletedCourses = await CourseTrackingModel.countDocuments({
      user: userId,
      courseComplateStatus: "completed",
    });
    return httpResponse(res, 200, "success", "Stats", {
      totalUsecaseUsage: totalUsage,
      totalUniqueUsecase: totalUniqueUsecase.length,
      totalSimpleApp: totalSimpleApp,
      totalWorkflow: totalWorkflow,
      totalJourney: totalJourneyItems,
      totalCertificates: totalCertificates,
      totalIdeas: totalIdeas,
      getUserCompletedCourses: getUserCompletedCourses,
      totalAIScore:
        totalUsage +
        totalUniqueUsecase.length * 2 +
        (totalSimpleApp + totalWorkflow) * 15 +
        totalJourneyItems * 10 +
        totalCertificates * 5 +
        totalIdeas * 10,
    });
  } catch (err) {
    console.error("Error fetching usage:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = { getStats };

const JourneyTrackingModel =
  require("../models/TrackingModel").JourneyTrackingModel;
const { httpResponse, sendEmail } = require("../utils/helpers");
const jwt = require("jsonwebtoken");
require("dotenv").config();
const fs = require("fs");
const path = require("path");
const mongoose = require("mongoose");
const OTPModel = require("../models/OTPModel");
const md5 = require("md5");
const sha256 = require("sha256");
const MailTemplate = require("../models/MailTemplate");
const UserModel = require("../models/UserModel");

exports.getJourneyTrackingById = async (req, res, next) => {
  const userId = req.params.id;
  try {
    const findUser = await JourneyTrackingModel.findById(userId);
    if (!findUser) {
      return httpResponse(res, 200, "success", "Journey Tracking not found.");
    }
    return httpResponse(res, 200, "success", "Journey Tracking Data", findUser);
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};

exports.updateJourneyTracking = async (req, res, next) => {
  const token = req.headers.authorization?.split(" ")[1];
  const userId = req.params.id;

  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }

  try {
    // Check if the user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      return httpResponse(res, 404, "error", "User not found."); // Return error if user not found
    }

    // Use the entire request body to update the JourneyTracking document
    const findUser = await JourneyTrackingModel.findByIdAndUpdate(
      userId,
      { $set: req.body.journeyTracking },
      { new: true, runValidators: true, upsert: true } // Upsert option
    );

    return httpResponse(
      res,
      200,
      "success",
      "Journey Tracking Updated or Created",
      findUser
    );
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};

// Kullanıcının gördüğü modalları kaydetmek için yeni metod
exports.updateSeenModals = async (req, res, next) => {
  const token = req.headers.authorization?.split(" ")[1];
  const userId = req.params.id;
  const { modalKey } = req.body;

  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }

  if (!modalKey) {
    return httpResponse(res, 400, "error", "Modal key is required.");
  }

  try {
    // Check if the user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      return httpResponse(res, 404, "error", "User not found.");
    }

    // Tracking verisini al ya da oluştur
    let trackingData = await JourneyTrackingModel.findById(userId);

    if (!trackingData) {
      // Yeni bir tracking verisi oluştur
      trackingData = new JourneyTrackingModel({
        _id: userId,
        beginner: {},
        expert: {},
        master: {},
        microsoft: {},
        seenModals: [],
      });
    }

    // Eğer modalKey zaten eklenmiş ise, herhangi bir değişiklik yapma
    if (!trackingData.seenModals || !Array.isArray(trackingData.seenModals)) {
      trackingData.seenModals = [];
    }

    // Modal daha önce görülmemiş ise ekle
    if (!trackingData.seenModals.includes(modalKey)) {
      trackingData.seenModals.push(modalKey);
      await trackingData.save();
    }

    return httpResponse(
      res,
      200,
      "success",
      "Seen modals updated",
      trackingData
    );
  } catch (err) {
    return httpResponse(res, 500, "error", "Server Error", err.message);
  }
};

const mongoose = require("mongoose");
const CompanyModel = require("../models/CompanyModel");
const {
  generateTrainingProgressReport,
} = require("./reporting/TrainingProgressController");
const {
  generateMSTrainingReport,
} = require("./reporting/MSTrainingController");
const {
  generateNextgenTalentReport,
} = require("./reporting/NextgenTalentController");
const {
  generateApplicationUsageReport,
} = require("./reporting/ApplicationUsageController");
const { generateAIValueReport } = require("./reporting/AIValueController");
const { generateIdeationReport } = require("./reporting/IdeationController");
const {
  generateCreatorToolsReport,
} = require("./reporting/CreatorToolsController");
const ReportModel = require("../models/ReportModel");
const REPORT_TYPES = {
  TRAINING: "training",
  NEXTGEN: "nextgen",
  APPLICATION: "application",
  AI_VALUE: "ai-value",
  IDEATION: "ideation",
  CREATOR: "creator",
  MS_TRAINING: "ms-training",
};

exports.getTrainingProgressReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const trainingProgressReport = await generateTrainingProgressReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(trainingProgressReport);
  } catch (error) {
    next(error);
  }
};

exports.getMSTrainingReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const msTrainingReport = await generateMSTrainingReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(msTrainingReport);
  } catch (error) {
    next(error);
  }
};

exports.getNextgenTalentReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const nextgenTalentReport = await generateNextgenTalentReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(nextgenTalentReport);
  } catch (error) {
    next(error);
  }
};

exports.getApplicationUsageReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const applicationUsageReport = await generateApplicationUsageReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(applicationUsageReport);
  } catch (error) {
    next(error);
  }
};

exports.getAIValueReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const aiValueReport = await generateAIValueReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(aiValueReport);
  } catch (error) {
    next(error);
  }
};

exports.getIdeationReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const ideationReport = await generateIdeationReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(ideationReport);
  } catch (error) {
    next(error);
  }
};

exports.getCreatorToolsReport = async (req, res, next) => {
  const {
    companyId,
    startDay,
    startMonth,
    startYear,
    endDay,
    endMonth,
    endYear,
  } = req.query;

  try {
    const creatorToolsReport = await generateCreatorToolsReport({
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    });

    return res.status(200).json(creatorToolsReport);
  } catch (error) {
    next(error);
  }
};

exports.createReport = async (req, res, next) => {
  try {
    const {
      reportType = req.params.type,
      companyId,
      startDay,
      startMonth,
      startYear,
      endDay,
      endMonth,
      endYear,
    } = req.body;

    const normalizedReportType = reportType.toLowerCase().replace(/_/g, "-");
    if (!Object.values(REPORT_TYPES).includes(normalizedReportType)) {
      return res.status(400).json({
        status: "error",
        message: `Invalid report type. Must be one of: ${Object.values(
          REPORT_TYPES
        ).join(", ")}`,
      });
    }

    if (!mongoose.Types.ObjectId.isValid(companyId) && CompanyModel) {
      return res.status(400).json({
        reportType: normalizedReportType,
        status: "error",
        message: "Invalid companyId format",
        data: null,
      });
    }

    const company = await CompanyModel.findById(companyId);
    if (!company) {
      return res.status(400).json({
        reportType: normalizedReportType,
        status: "error",
        message: "Company not found",
        data: null,
      });
    }

    let report = {
      type: normalizedReportType,
      companyId,
      generatedAt: new Date(),
      data: null,
    };

    switch (normalizedReportType) {
      case REPORT_TYPES.TRAINING:
        report.data = await generateTrainingProgressReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.NEXTGEN:
        report.data = await generateNextgenTalentReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.APPLICATION_USAGE:
        report.data = await generateApplicationUsageReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.AI_VALUE:
        report.data = await generateAIValueReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.IDEATION:
        report.data = await generateIdeationReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      case REPORT_TYPES.CREATOR_TOOLS:
        report.data = await generateCreatorToolsReport({
          companyId,
          startDay,
          startMonth,
          startYear,
          endDay,
          endMonth,
          endYear,
        });
        break;
      default:
        return res.status(400).json({ message: "Invalid report type" });
    }

    return res.status(200).json(report);
  } catch (error) {
    next(error);
  }
};

exports.getReportFromModel = async (req, res, next) => {
  const { reportType, companyId } = req.params;

  const report = await ReportModel.find({
    reportType,
    company: companyId,
  });

  return res.status(200).json(report);
};

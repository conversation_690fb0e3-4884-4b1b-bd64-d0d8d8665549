const ToolkitModel = require("../models/ToolkitModel");
const User = require("../models/UserModel");
const { httpResponse } = require("../utils/helpers");

exports.addToolkit = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  const { toolkitItems, toolkitCompany, toolkitUser } = req.body;
  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }
  try {
    const createToolkit = await ToolkitModel.create({
      toolkitItems,
      toolkitCompany: toolkitCompany,
      toolkitUser: toolkitUser,
    });
    return httpResponse(res, 200, "success", "Toolkit Created", createToolkit);
  } catch (err) {
    console.log("error.message:", err.message);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      err.message
    );
  }
};
exports.getToolkits = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }
  try {
    const toolkits = await ToolkitModel.find({}).populate(
      "toolkitCompany toolkitUser"
    );
    return httpResponse(
      res,
      200,
      "success",
      "Toolkits retrieved successfully",
      toolkits
    );
  } catch (err) {
    console.log("error.message:", err.message);
  }
};

exports.getToolkitsByCompany = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  const companyId = req.params.id;

  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }
  try {
    const toolkits = await ToolkitModel.find({
      toolkitCompany: companyId,
    }).populate("toolkitCompany");

    if (!toolkits || toolkits.length === 0) {
      return httpResponse(
        res,
        200,
        "error",
        "No toolkits found for this company"
      );
    }

    return httpResponse(
      res,
      200,
      "success",
      "Toolkits retrieved successfully",
      toolkits
    );
  } catch (err) {
    console.log("error.message:", err.message);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      err.message
    );
  }
};

exports.updateToolkit = async (req, res, next) => {
  var token = req.headers.authorization?.split(" ")[1];
  const { id } = req.params;

  if (!token) {
    return httpResponse(res, 401, "error", "No token provided.");
  }

  try {
    const { toolkitItems, toolkitCompany, toolkitUser } = req.body;
    const updatedToolkit = await ToolkitModel.findByIdAndUpdate(
      id,
      { toolkitItems, toolkitCompany, toolkitUser },
      { new: true }
    ).populate("toolkitCompany toolkitUser");

    if (!updatedToolkit) {
      return httpResponse(res, 404, "error", "Toolkit not found");
    }

    return httpResponse(
      res,
      200,
      "success",
      "Toolkit updated successfully",
      updatedToolkit
    );
  } catch (err) {
    console.log("error.message:", err.message);
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      err.message
    );
  }
};

const axios = require("axios");
const { httpResponse } = require("../utils/helpers");
const { JSDOM } = require("jsdom");

exports.getProjects = async (req, res, next) => {
  const { platformUrl } = req.body;
  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/getProjectList`,
      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );

    return httpResponse(res, 200, "success", "All Projects", response.data);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};
exports.getIdeations = async (req, res, next) => {
  const { platformUrl, projectId, length, start } = req.body;
  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/getAll?project_id=${projectId}&length=${length}&start=${start}`,
      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );
    return httpResponse(res, 200, "success", "Ideations", response.data);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};
exports.getIdeationDescription = async (req, res, next) => {
  const { platformUrl, ideationID } = req.body;
  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/get?id=${ideationID}`,
      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );

    return httpResponse(
      res,
      200,
      "success",
      "Idea description",
      response?.data
    );
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};
exports.getQuestionList = async (req, res, next) => {
  const { platformUrl, projectID } = req.body;

  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/getQuestionList?project_id=${projectID}`,
      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );

    return httpResponse(res, 200, "success", "Idea description", response.data);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};
exports.getAnalyticData = async (req, res, next) => {
  const { platformUrl, projectID, type, question_name } = req.body;

  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/getAnalyticData?project_id=${projectID}&tab_filter=${type}&question_name=${question_name}`,

      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );
    return httpResponse(res, 200, "success", "Idea description", response.data);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};
exports.getTabFilterData = async (req, res, next) => {
  const { platformUrl, projectID } = req.body;

  try {
    const response = await axios.get(
      `${platformUrl}/api/ideation/tabFilterData?project_id=${projectID}`,

      {
        headers: {
          "X-Api-Key": process.env.AITRAINER_API_KEY,
          "X-Token": process.env.AITRAINER_API_TOKEN,
        },
      }
    );
    return httpResponse(res, 200, "success", "Idea description", response.data);
  } catch (error) {
    return httpResponse(
      res,
      500,
      "error",
      "An internal server error occurred",
      error.response?.data || error.message
    );
  }
};

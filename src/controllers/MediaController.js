const {
  BlobServiceClient,
  generateBlobSASQueryParameters,
} = require("@azure/storage-blob");
const { httpResponse } = require("../utils/helpers");
const multer = require("multer");
const ENUM = require("../utils/enum");

// Multer için dosya boyutu limitini ayarla (örneğin 100MB)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
}).single("media");

const azureStorageConnectionString =
  process.env.AZURE_STORAGE_CONNECTION_STRING;

exports.addMedia = async (req, res, next) => {
  upload(req, res, async function (err) {
    if (err instanceof multer.MulterError) {
      if (err.code === "LIMIT_FILE_SIZE") {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.REQUEST_ENTITY_TOO_LARGE,
          "error",
          "File size too large",
          "Maximum file size allowed is 100MB"
        );
      }
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "File upload error",
        err.message
      );
    } else if (err) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "Unexpected error occurred",
        err.message
      );
    }

    if (!req.file) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Please upload a file",
        null
      );
    }

    const { containerName } = req.body;

    if (!containerName) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Container name is required",
        null
      );
    }

    try {
      const mediaBuffer = req.file.buffer;
      const blobName = req.file.originalname;

      const blobServiceClient = BlobServiceClient.fromConnectionString(
        azureStorageConnectionString
      );
      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      await blockBlobClient.upload(mediaBuffer, mediaBuffer.length);

      // SAS URL'si için gerekli parametreler - gerekirse başka bir endpoint için kullanılabilir
      const sasToken = generateBlobSASQueryParameters(
        {
          containerName: containerName,
          blobName: blobName,
          permissions: "r", // yalnızca okuma izni
          expiresOn: new Date(new Date().valueOf() + 3600 * 1000), // 1 saat geçerlilik süresi
        },
        blobServiceClient.credential
      ).toString();

      // Blob için erişilebilir URL (SAS token olmadan)
      const blobUrl = blockBlobClient.url;

      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "File uploaded successfully",
        {
          fileName: blobName,
          containerName: containerName,
          size: mediaBuffer.length,
          url: blobUrl,
        }
      );
    } catch (error) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "Server error",
        error.message
      );
    }
  });
};
exports.listMedia = async (req, res, next) => {
  const { containerName } = req.query;
  try {
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      azureStorageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobs = [];

    for await (const blob of containerClient.listBlobsFlat()) {
      // SAS URL oluşturmak için blob ismi ile bir BlobClient yaratıyoruz
      const blobClient = containerClient.getBlobClient(blob.name);

      // Blob'un metadata bilgilerini alıyoruz
      const blobProperties = await blobClient.getProperties();

      // SAS URL'si için gerekli parametreler
      const sasToken = generateBlobSASQueryParameters(
        {
          containerName: containerName,
          blobName: blob.name,
          permissions: "r", // yalnızca okuma izni
          expiresOn: new Date(new Date().valueOf() + 3600 * 1000), // 1 saat geçerlilik süresi
        },
        blobServiceClient.credential
      ).toString();

      // Blob için erişilebilir URL
      const blobUrl = `${blobClient.url}?${sasToken}`;

      // Blob bilgilerine URL ve metadata ekleyin
      blobs.push({
        name: blob.name,
        url: blobUrl,
        ...blob.properties,
        metadata: blobProperties.metadata, // Metadata bilgisi burada ekleniyor
      });
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "media list",
      blobs
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "server error",
      error.message
    );
  }
};
exports.deleteMedia = async (req, res, next) => {
  const { containerName, blobName } = req.query;
  try {
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      azureStorageConnectionString
    );
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const deleteBlockBlobResponse = await blockBlobClient.delete();
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "media deleted successfully",
      deleteBlockBlobResponse
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "server error",
      error.message
    );
  }
};

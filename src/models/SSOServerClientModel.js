const mongoose = require('mongoose');

const SSOServerClientSchema = new mongoose.Schema({
    clientId: {
        type: String,
        required: true,
        unique: true
    },
    clientSecret: {
        type: String,
        required: true
    },
    redirectUris: [{
        type: String,
        required: true
    }],
    name: String,
    description: String,
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('SSOServerClient', SSOServerClientSchema); 
const mongoose = require("mongoose");

// Sub-schemas
const FormFieldSchema = new mongoose.Schema({
  type: String,
  choices: String,
  label: String,
  name: String,
  default_value: String,
});
 

const UseCaseSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
    },
    description: String, 
    model: String,
    prompt: String,
    simple_app_form: [FormFieldSchema],  
    temperature: {
      type: String,
      default: "1"
    },
    top_p: {
      type: String,
      default: "1"
    },
    presence_penalty: {
      type: String,
      default: "0"
    },
    frequency_penalty: {
      type: String,
      default: "0"
    }, 
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users", require: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        const functionOrder = ret.function_order;
        delete ret.function_order;
        ret.function_order = functionOrder;
        return ret;
      },
    },
  }
);

module.exports = mongoose.model("UseCase", UseCaseSchema);

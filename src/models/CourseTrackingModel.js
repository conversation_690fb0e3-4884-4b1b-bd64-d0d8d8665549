const mongoose = require("mongoose");

const videoProgressSchema = new mongoose.Schema({
  watchedDuration: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalDuration: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastWatchPosition: {
    type: Number,
    default: 0,
    min: 0,
  },
  completed: {
    type: Boolean,
    default: false,
  },
  lastAccessed: {
    type: Date,
    default: Date.now,
  },
});

const topicProgressSchema = new mongoose.Schema({
  topicId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Topic ID is required"],
  },
  completed: {
    type: Boolean,
    default: false,
  },
  videoProgress: {
    type: videoProgressSchema,
    default: null,
  },
  startedAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: Date,
  lastAccessed: {
    type: Date,
    default: Date.now,
  },
});

const chapterProgressSchema = new mongoose.Schema({
  chapterId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Chapter ID is required"],
  },
  completed: {
    type: Boolean,
    default: false,
  },
  topics: {
    type: [topicProgressSchema],
    default: [],
  },
  startedAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: Date,
  lastAccessed: {
    type: Date,
    default: Date.now,
  },
});

const courseTrackingSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
      required: [true, "Progress must belong to a user"],
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Course",
      required: [true, "Progress must belong to a course"],
    },
    courseComplateStatus: {
      type: String,
      enum: {
        values: ["progress", "completed"],
        message: "{VALUE} is not a valid completion status",
      },
      default: "progress",
    },
    chapters: [chapterProgressSchema],
    startedAt: {
      type: Date,
      default: Date.now,
    },
    lastAccessed: {
      type: Date,
      default: Date.now,
    },
    completedAt: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    versionKey: false,
  }
);

courseTrackingSchema.index({ user: 1, course: 1 }, { unique: true });

courseTrackingSchema.virtual("progressPercentage").get(function () {
  const totalTopics = this.chapters.reduce(
    (acc, chapter) => acc + chapter.topics.length,
    0
  );

  if (totalTopics === 0) return 0;

  const completedTopics = this.chapters.reduce(
    (acc, chapter) =>
      acc + chapter.topics.filter((topic) => topic.completed).length,
    0
  );

  return Math.round((completedTopics / totalTopics) * 100);
});

const CourseTracking = mongoose.model("CourseTracking", courseTrackingSchema);

module.exports = CourseTracking;

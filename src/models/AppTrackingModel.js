const mongoose = require("mongoose");

const AppTrackingSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "users",
    required: true,
  },
  appId: { type: String, required: true },
  appType: {
    type: String,
    required: true,
    enum: [
      "text-usecase",
      "course",
      "heygen",
      "image-generation",
      "video-generation",
      "assistant",
      "ideation",
      "toolkit",
      "workflow",
      "simple-app",
    ],
  },
  year: { type: Number, required: true },
  month: { type: Number, required: true },
  day: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  count: { type: Number, default: 0 },
});

AppTrackingSchema.index(
  {
    userId: 1,
    appType: 1,
    appId: 1,
    year: 1,
    month: 1,
    day: 1,
  },
  { unique: true }
);

module.exports = mongoose.model("apptrackings", AppTrackingSchema);

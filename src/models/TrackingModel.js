const mongoose = require("mongoose");

const JourneyTrackingSchema = mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users" },
  beginner: Object,
  expert: Object,
  master: Object,
  microsoft: Object,
  seenModals: { type: Array, default: [] },
});

const AppTrackingSchema = mongoose.Schema({
  event: String,
  timestamp: { type: Date, default: Date.now },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users" }, // Reference to Users collection
});

const JourneyTrackingModel =
  mongoose.models.JourneyTracking ||
  mongoose.model("JourneyTracking", JourneyTrackingSchema);
const AppTrackingModel =
  mongoose.models.AppTracking ||
  mongoose.model("AppTracking", AppTrackingSchema);

module.exports = {
  JourneyTrackingModel,
  AppTrackingModel,
};

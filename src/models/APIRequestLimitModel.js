const mongoose = require("mongoose");

const APIRequestLimitSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    endpoint: {
      type: String,
      required: true,
      default: "textGeneration",
    },
    dailyUsage: {
      count: {
        type: Number,
        default: 0,
      },
      limit: {
        type: Number,
        default: 10000, // Varsayılan günlük limit
      },
      resetDate: {
        type: Date,
        default: function () {
          const now = new Date();
          const tomorrow = new Date(now);
          tomorrow.setDate(tomorrow.getDate() + 1);
          tomorrow.setHours(0, 0, 0, 0);
          return tomorrow;
        },
      },
    },
    weeklyUsage: {
      count: {
        type: Number,
        default: 0,
      },
      limit: {
        type: Number,
        default: 10000, // Varsayılan haftalık limit
      },
      resetDate: {
        type: Date,
        default: function () {
          const now = new Date();
          const nextWeek = new Date(now);
          nextWeek.setDate(nextWeek.getDate() + (7 - nextWeek.getDay()));
          nextWeek.setHours(0, 0, 0, 0);
          return nextWeek;
        },
      },
    },
    monthlyUsage: {
      count: {
        type: Number,
        default: 0,
      },
      limit: {
        type: Number,
        default: 10000, // Varsayılan aylık limit
      },
      resetDate: {
        type: Date,
        default: function () {
          const now = new Date();
          const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
          nextMonth.setHours(0, 0, 0, 0);
          return nextMonth;
        },
      },
    },
    lastUsageDate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

// Bileşik index, aynı kullanıcının aynı endpoint için birden fazla kayıt oluşturmasını engeller
APIRequestLimitSchema.index({ userId: 1, endpoint: 1 }, { unique: true });

module.exports = mongoose.model("APIRequestLimit", APIRequestLimitSchema);

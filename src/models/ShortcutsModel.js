const mongoose = require('mongoose');

const ShortcutsSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'users', 
    required: true 
  },
  shortcutType: { 
    type: String, 
    required: true, 
    enum: ["page", "usecase", "course", "workflow", "playground", "prompt"] 
  },  
  shortcutUrl: {
    // Sadece shortcutType "page" olduğunda gerekli
    type: String,
    required: function() {
      return this.shortcutType === "page";
    }
  },
  shortcutID: {
    // "page" dışındaki türler için gerekli
    type: mongoose.Schema.Types.ObjectId,
    required: function() {
      return this.shortcutType !== "page";
    }
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  } 
});

// 1. Partial index: Sadece shortcutType "page" olan dökümanlar için, 
// userId, shortcutType ve shortcutUrl kombinasyonunun benzersiz olmasını sağlar.
ShortcutsSchema.index(
  { userId: 1, shortcutType: 1, shortcutUrl: 1 },
  { unique: true, partialFilterExpression: { shortcutType: "page" } }
);

// 2. Partial index: shortcutType "page" olmayan dökümanlarda, 
// userId, shortcutType ve shortcutID kombinasyonunun benzersiz olmasını sağlar.
ShortcutsSchema.index(
  { userId: 1, shortcutType: 1, shortcutID: 1 },
  { unique: true, partialFilterExpression: { shortcutType: { $ne: "page" } } }
);

module.exports = mongoose.model('shortcuts', ShortcutsSchema);

const mongoose = require('mongoose');

const SSOServerAuthCodeSchema = new mongoose.Schema({
    code: {
        type: String,
        required: true,
        unique: true
    },
    clientId: {
        type: String,
        required: true
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    redirectUri: {
        type: String,
        required: true
    },
    expiresAt: {
        type: Date,
        required: true
    },
    scope: String
});

module.exports = mongoose.model('SSOServerAuthCode', SSOServerAuthCodeSchema); 
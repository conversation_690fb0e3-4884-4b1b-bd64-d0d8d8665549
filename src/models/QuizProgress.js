const mongoose = require("mongoose");

const quizProgressSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    courseId: {
      type: String,
      required: true,
    },
    chapterId: {
      type: String,
      required: true,
    },
    topicId: {
      type: String,
      required: true,
    },
    completed: {
      type: Boolean,
      default: false,
    },
    score: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    answers: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
    attemptCount: {
      type: Number,
      default: 1,
    },
    lastAttemptDate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Bileşik index oluştur
quizProgressSchema.index(
  { userId: 1, courseId: 1, chapterId: 1, topicId: 1 },
  { unique: true }
);

module.exports = mongoose.model("QuizProgress", quizProgressSchema);

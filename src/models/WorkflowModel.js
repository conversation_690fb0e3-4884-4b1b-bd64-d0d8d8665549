const mongoose = require('mongoose');

// Sub-schemas
const FormFieldSchema = new mongoose.Schema({
  type: String,
  label: String,
  name: String,
  value: String
});
 

const WorkflowSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true
  },
  slug: {
    type: String,
    required: true,
    unique: true
  },  
  workflow_form: [FormFieldSchema],
  temperature: {
    type: String,
    default: "1"
  },
  top_p: {
    type: String,
    default: "1"
  },
  presence_penalty: {
    type: String,
    default: "0"
  },
  frequency_penalty: {
    type: String,
    default: "0"
  }, 
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "Users", require: true },
 
});

module.exports = mongoose.model('Workflow', WorkflowSchema);
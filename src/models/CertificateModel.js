const mongoose = require("mongoose");

const certificateSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Course",
      required: true,
    },
    fullName: {
      type: String,
      required: true,
      trim: true,
    },
    certificateName: {
      type: String,
      required: true,
      trim: true,
    },
    organizationName: {
      type: String,
      required: true,
      default: "Default Organization",
      trim: true,
    },
    instructorName: {
      type: String,
      required: true,
      default: "Default Instructor",
      trim: true,
    },
    completionTime: {
      type: String,
      required: true,
      default: "40 Hours",
    },
    certificateNumber: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    securityCode: {
      type: String,
      required: true,
    },
    fileName: {
      type: String,
      required: true,
    },
    downloadUrl: {
      type: String,
      required: true,
    },
    qrCode: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ["active", "revoked"],
      default: "active",
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    issueDate: {
      type: Date,
      required: true,
    },
    metaData: {
      type: Map,
      of: String,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Sertifika numarası ve güvenlik kodu için index
certificateSchema.index({ certificateNumber: 1, securityCode: 1 });

// Kullanıcı ve kurs için unique index
certificateSchema.index({ userId: 1, courseId: 1 }, { unique: true });

module.exports = mongoose.model("Certificate", certificateSchema);

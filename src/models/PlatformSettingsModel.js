const mongoose = require("mongoose");

const RuleSchema = mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId(),
  },
  field: String,
  operator: String,
  value: mongoose.Schema.Types.Mixed,
});

const RuleGroupSchema = mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId(),
  },
  type: {
    type: String,
    enum: ["RULE", "GROUP"],
  },
  logicalOperator: {
    type: String,
    enum: ["AND", "OR"],
    default: "AND",
  },
  rules: [
    {
      type: mongoose.Schema.Types.Mixed, // Bu alan ya RuleSchema ya da RuleGroupSchema olabilir
      required: true,
    },
  ],
});

const PlatformSettingsSchema = mongoose.Schema({
  general: {
    platformName: String,
    platformEmail: String,
    platformLogo: String,
    platformURL: String,
    maintenanceMode: Boolean,
    maintenanceModeAllowedUsers: Object,
    maintenanceModeAllowedRoles: Object,
    googleRecaptchaV2: {
      siteKey: String,
      secretKey: String,
    },
    googleRecaptchaV3: {
      siteKey: String,
      secretKey: String,
    },
  },
  user: {
    changePassword: Boolean,
    personalInformation: Boolean,
    allowResetUserData: Boolean,
    allowedRolesForResetData: Object,
    allowedUsersForResetData: Object,
  },
  ssoSettings: {
    isEnabled: Boolean,
    msalClientId: String,
    msalTenantId: String,
    msalSecret: String,
    msalSecretValue: String,
  },
  userSegmentation: {
    segments: [
      {
        _id: {
          type: mongoose.Schema.Types.ObjectId,
          default: () => new mongoose.Types.ObjectId(),
        },
        name: String,
        priority: Number,
        description: String,
        ruleGroup: RuleGroupSchema,
      },
    ],
  },
  security: {
    allowedRetries: Number,
    lockoutTime: Number,
    showRecaptcha: Boolean,
    vpnStatus: Boolean,
    allowedIPs: Object,

    roleBasedSessionExpireTimes: Object,
  },
});

const PlatformSettingsModel =
  mongoose.models.PlatformSettings ||
  mongoose.model("PlatformSettings", PlatformSettingsSchema);

module.exports = PlatformSettingsModel;

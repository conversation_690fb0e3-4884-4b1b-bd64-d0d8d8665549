const mongoose = require("mongoose");

const ToolkitSchema = mongoose.Schema(
  {
    toolkitItems: { type: Object, require: true }, 
    toolkitCompany : { type: mongoose.Schema.Types.ObjectId, ref: "Company", require: true },
    toolkitUser : { type: mongoose.Schema.Types.ObjectId, ref: "Users", require: true },
  },
  { versionKey: false }
);

module.exports = mongoose.model("Toolkit", ToolkitSchema);

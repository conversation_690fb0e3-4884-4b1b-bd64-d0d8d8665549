const mongoose = require("mongoose");

const ReportSchema = mongoose.Schema(
  {
    company: { type: mongoose.Schema.Types.ObjectId, ref: "Company" },
    reportType: {
      type: String,
      enum: [
        "training",
        "nextgen",
        "ideation",
        "creator",
        "application",
        "ai-value",
        "ms-training",
      ],
      require: true,
    },
    reportData: {
      type: mongoose.Schema.Types.Mixed,
      require: true,
    },
    downloadLink: { type: String, require: true },
    createdAt: { type: String, require: true },
  },
  { versionKey: false }
);

module.exports = mongoose.model("Report", ReportSchema);

const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");

/**
 * Admin Yetkilendirme Middleware
 * Kullanıcının admin yetkisi olup olmadığını kontrol eder.
 * Sadece req.user.role değeri "admin" veya "SUPER_USER" olan kullanıcılara izin verir.
 */
const adminAuthMiddleware = (req, res, next) => {
  try {
    // Kullanıcı bilgisi kontrolü - authMiddleware tarafından eklenir
    if (!req.user) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.UNAUTHORIZED,
        "error",
        "Kimlik doğrulama gerekli",
        null
      );
    }

    // Admin yetkisi kontrolü
    const isAdmin =
      req.user.role === "admin" ||
      req.user.admin === true ||
      req.user.role === "SUPER_USER";

    if (!isAdmin) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.FORBIDDEN,
        "error",
        "Bu işlem için admin yetkisi gerekiyor",
        null
      );
    }

    // Yetki doğrulaması başarılı, bir sonraki middleware'e geçiş
    next();
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Yetkilendirme hatası",
      error.message
    );
  }
};

module.exports = adminAuthMiddleware;

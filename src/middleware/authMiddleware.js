const jwt = require("jsonwebtoken");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");
const config = require("../../config");
const UserModel = require("../models/UserModel");

const AuthMiddleware = (requireAdmin = false) => {
  return (req, res, next) => {
    if (req.isApiKeyAuthenticated) {
      return next();
    }

    try {
      const authHeader = req.headers["authorization"];

      if (!authHeader) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.UNAUTHORIZED,
          "error",
          "Access Denied: No Token Provided"
        );
      }

      const token = authHeader.split(" ")[1];

      jwt.verify(token, config.JWT_SECRET, async (err, decoded) => {
        if (err) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.UNAUTHORIZED,
            "error",
            "Invalid Token",
            err.message
          );
        }

        req.user = decoded;

        const findUserID = await UserModel.findOne({ email: req.user.email });

        // Eğer admin yetkisi gerekiyorsa kontrol et
        if (requireAdmin) {
          const isAdmin =
            findUserID.role === "admin" ||
            findUserID.role === "Administrator" ||
            findUserID.role === "SUPER_USER";

          if (!isAdmin) {
            return httpResponse(
              res,
              ENUM.HTTP_CODES.FORBIDDEN,
              "error",
              "This action requires admin privileges",
              null
            );
          }
        }

        next();
      });
    } catch (error) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.INT_SERVER_ERROR,
        "error",
        "Authentication error",
        error.message
      );
    }
  };
};

module.exports = AuthMiddleware;

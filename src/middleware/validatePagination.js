const { httpResponse } = require("../utils/helpers");

const validatePagination = (req, res, next) => {
  const { page, limit } = req.query;

  // Validate page
  if (page && (!Number.isInteger(Number(page)) || Number(page) < 1)) {
    return httpResponse(res, 400, "error", "Invalid page parameter. Must be a positive integer.");
  }

  // Validate limit
  if (limit && (!Number.isInteger(Number(limit)) || Number(limit) < -1  )) {
    return httpResponse(res, 400, "error", "Invalid limit parameter. Must be a positive integer between 1 and 100.");
  }

  // Check if limit is -1 (as a number or string)
  if (Number(limit) === -1) { 
    // set unlimited if limit = -1 
    req.query.limit = -1;
  }

  next();
};

module.exports = validatePagination; 
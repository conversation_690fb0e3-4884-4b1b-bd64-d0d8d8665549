const jwt = require('jsonwebtoken');
const { httpResponse } = require('../utils/helpers');
const mongoose = require('mongoose');
const UserModel = require('../models/UserModel');

const verifySSOToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return httpResponse(res, 401, "error", "No token provided");
        }

        const token = authHeader.split(' ')[1];
        
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Token içeriğini kontrol et
            if (decoded.email) {
                // Email varsa, kullanıcıyı email ile bul (authorize endpoint için)
                const user = await UserModel.findOne({ email: decoded.email });
                if (!user) {
                    return httpResponse(res, 404, "error", "User not found");
                }
                req.userId = user._id;
                req.email = decoded.email;
            } else if (decoded.userId) {
                // UserId varsa direkt kullan (resources endpoint için)
                if (!mongoose.Types.ObjectId.isValid(decoded.userId)) {
                    return httpResponse(res, 401, "error", "Invalid user ID in token");
                }
                req.userId = new mongoose.Types.ObjectId(decoded.userId);
            } else {
                return httpResponse(res, 401, "error", "Invalid token content");
            }

            req.scope = decoded.scope;
            next();
        } catch (err) {
            console.error('Token verification error:', err);
            return httpResponse(res, 401, "error", "Invalid or expired token");
        }
    } catch (error) {
        console.error('Auth middleware error:', error);
        return httpResponse(res, 500, "error", "Authentication error", error.message);
    }
};

module.exports = { verifySSOToken }; 
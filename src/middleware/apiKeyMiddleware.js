const { httpResponse } = require("../utils/helpers");
const ENUM = require("../utils/enum");
const config = require("../../config");

const apiKeyMiddleware = (req, res, next) => {
  try {
    // API Key'i al ve boşlukları temizle
    const apiKey = req.headers["x-api-key"]
      ? req.headers["x-api-key"].trim()
      : null;

    // API Key yoksa, normal auth akışına devam et
    if (!apiKey) {
      return next();
    }

    // Geçerli API Key'leri kontrol et
    const validApiKeys = [
      config.API_KEY_ADMIN,
      config.API_KEY_REPORTING,
      config.API_KEY_READONLY,
    ];

    console.log("API Key Middleware - Valid API Keys:", validApiKeys);

    // Büyük/küçük harf duyarlılığını kaldırmak için API Key'leri küçük harfe çevirelim
    const normalizedApiKey = apiKey.toLowerCase();
    const normalizedValidApiKeys = validApiKeys.map((key) =>
      key ? key.toLowerCase() : key
    );

    console.log("API Key Middleware - Normalized API Key:", normalizedApiKey);
    console.log(
      "API Key Middleware - Normalized Valid API Keys:",
      normalizedValidApiKeys
    );

    if (normalizedValidApiKeys.includes(normalizedApiKey)) {
      console.log("API Key Middleware - Valid API Key found");
      req.isApiKeyAuthenticated = true;

      // API Key'e göre rol belirle (büyük/küçük harf duyarlılığı olmadan)
      if (normalizedApiKey === (config.API_KEY_ADMIN || "").toLowerCase()) {
        req.apiKeyRole = "admin";
      } else if (
        normalizedApiKey === (config.API_KEY_REPORTING || "").toLowerCase()
      ) {
        req.apiKeyRole = "reporting";
      } else if (
        normalizedApiKey === (config.API_KEY_READONLY || "").toLowerCase()
      ) {
        req.apiKeyRole = "readonly";
      }

      console.log("API Key Middleware - Role assigned:", req.apiKeyRole);
      return next();
    }

    console.log("API Key Middleware - Invalid API Key");
    // API Key geçersizse, normal auth akışına devam et
    next();
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API Key authentication error",
      error.message
    );
  }
};

module.exports = apiKeyMiddleware;

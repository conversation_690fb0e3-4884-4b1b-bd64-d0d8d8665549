const APIRequestLimitModel = require("../models/APIRequestLimitModel");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");
const UserModel = require("../models/UserModel");
/**
 * API İstek Limiti Middleware'i
 * Kullanıcıların API istek limitlerini kontrol eder.
 * @param {string} endpoint - Kontrol edilecek API endpoint'i (varsayılan: "textGeneration")
 * @returns {function} Middleware fonksiyonu
 */
const apiRequestLimitMiddleware = (endpoint = "textGeneration") => {
  return async (req, res, next) => {
    try {
      // Kullanıcı kimliğini kontrol et
      const userId = req.user?.email;
      const findUserID = await UserModel.findOne({ email: userId });

      if (!findUserID._id) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.UNAUTHORIZED,
          "error",
          "Authentication required",
          null
        );
      }

      // Kullanıcının API istek limitlerini kontrol et
      let userLimits = await APIRequestLimitModel.findOne({
        userId: findUserID._id,
        endpoint,
      });

      // Kullanıcının limit kaydı yoksa yeni bir kayıt oluştur
      if (!userLimits) {
        userLimits = new APIRequestLimitModel({
          userId: findUserID._id,
          endpoint,
        });
      }

      // Mevcut zamanı al
      const now = new Date();

      // Günlük limit kontrolü
      if (now >= userLimits.dailyUsage.resetDate) {
        userLimits.dailyUsage.count = 0;
        userLimits.dailyUsage.resetDate = new Date(now);
        userLimits.dailyUsage.resetDate.setDate(
          userLimits.dailyUsage.resetDate.getDate() + 1
        );
        userLimits.dailyUsage.resetDate.setHours(0, 0, 0, 0);
      }

      // Haftalık limit kontrolü
      if (now >= userLimits.weeklyUsage.resetDate) {
        userLimits.weeklyUsage.count = 0;
        userLimits.weeklyUsage.resetDate = new Date(now);
        userLimits.weeklyUsage.resetDate.setDate(
          userLimits.weeklyUsage.resetDate.getDate() +
            (7 - userLimits.weeklyUsage.resetDate.getDay())
        );
        userLimits.weeklyUsage.resetDate.setHours(0, 0, 0, 0);
      }

      // Aylık limit kontrolü
      if (now >= userLimits.monthlyUsage.resetDate) {
        userLimits.monthlyUsage.count = 0;
        userLimits.monthlyUsage.resetDate = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          1
        );
        userLimits.monthlyUsage.resetDate.setHours(0, 0, 0, 0);
      }

      // Limitleri aşıp aşmadığını kontrol et
      if (userLimits.dailyUsage.count >= userLimits.dailyUsage.limit) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.TOO_MANY_REQUESTS,
          "error",
          "Daily API request limit exceeded. Please try again tomorrow.",
          {
            currentUsage: userLimits.dailyUsage.count,
            limit: userLimits.dailyUsage.limit,
            resetDate: userLimits.dailyUsage.resetDate,
          }
        );
      }

      if (userLimits.weeklyUsage.count >= userLimits.weeklyUsage.limit) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.TOO_MANY_REQUESTS,
          "error",
          "Weekly API request limit exceeded. Please try again next week.",
          {
            currentUsage: userLimits.weeklyUsage.count,
            limit: userLimits.weeklyUsage.limit,
            resetDate: userLimits.weeklyUsage.resetDate,
          }
        );
      }

      if (userLimits.monthlyUsage.count >= userLimits.monthlyUsage.limit) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.TOO_MANY_REQUESTS,
          "error",
          "Monthly API request limit exceeded. Please try again next month.",
          {
            currentUsage: userLimits.monthlyUsage.count,
            limit: userLimits.monthlyUsage.limit,
            resetDate: userLimits.monthlyUsage.resetDate,
          }
        );
      }

      // Kullanım sayaçlarını artır
      userLimits.dailyUsage.count += 1;
      userLimits.weeklyUsage.count += 1;
      userLimits.monthlyUsage.count += 1;
      userLimits.lastUsageDate = now;

      // Kullanım kayıtlarını asenkron olarak kaydet - isteği bekletme
      userLimits.save().catch((error) => {
        console.error("API request limit save error:", error);
      });

      // İstek değişkenlerine limit bilgilerini ekle (geliştirme/hata ayıklama için faydalı olabilir)
      req.requestLimits = {
        daily: {
          count: userLimits.dailyUsage.count,
          limit: userLimits.dailyUsage.limit,
          resetDate: userLimits.dailyUsage.resetDate,
        },
        weekly: {
          count: userLimits.weeklyUsage.count,
          limit: userLimits.weeklyUsage.limit,
          resetDate: userLimits.weeklyUsage.resetDate,
        },
        monthly: {
          count: userLimits.monthlyUsage.count,
          limit: userLimits.monthlyUsage.limit,
          resetDate: userLimits.monthlyUsage.resetDate,
        },
      };

      // Sonraki middleware'e geç
      next();
    } catch (error) {
      console.error("API request limit control error:", error);
      // Hata durumunda, güvenlik için isteğe izin ver
      next();
    }
  };
};

module.exports = apiRequestLimitMiddleware;

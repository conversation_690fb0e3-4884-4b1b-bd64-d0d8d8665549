const express = require("express");
const {
  addAPI,
  getAPIs,
  getAPIById,
  editAPI,
  deleteAPI,
  textGeneration,
  imageGeneration,
  videoGeneration,
  getHeygenAvatars,
  getHeygenVoices,
  getHeygenVideo,
  getAssistants,
  getHeygenAvatarsLocal,
  assistant,
} = require("../controllers/ConnectorsController");
const auth = require("../middleware/authMiddleware");
const apiRequestLimitMiddleware = require("../middleware/apiRequestLimitMiddleware");
const validatePagination = require("../middleware/validatePagination");

const router = express.Router();

// Ana API yönetim route'ları
router.route("/").post(auth(), addAPI).get(auth(), getAPIs);

// Generation route'ları
router
  .route("/text-generation")
  .post(auth(), apiRequestLimitMiddleware("textGeneration"), textGeneration);
router
  .route("/image-generation")
  .post(auth(), apiRequestLimitMiddleware("imageGeneration"), imageGeneration);
router
  .route("/video-generation")
  .post(auth(), apiRequestLimitMiddleware("videoGeneration"), videoGeneration);

// Assistant route'u
router
  .route("/assistant")
  .post(auth(), apiRequestLimitMiddleware("assistant"), assistant);

// Heygen özel route'ları
router
  .route("/heygen/avatars/local")
  .get(auth(), validatePagination, getHeygenAvatarsLocal);
router
  .route("/heygen/avatars")
  .get(auth(), validatePagination, getHeygenAvatars);
router.route("/heygen/voices").get(auth(), validatePagination, getHeygenVoices);
router.route("/heygen/video/:video_id").get(auth(), getHeygenVideo);

// Assistant listesi route'u
router.route("/assistants").get(auth(), getAssistants);

// API yönetim route'ları
router
  .route("/:id")
  .get(auth(), getAPIById)
  .patch(auth(), editAPI)
  .delete(auth(), deleteAPI);

module.exports = router;

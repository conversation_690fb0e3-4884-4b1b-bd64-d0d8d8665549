const express = require("express");
const router = express.Router();
const certificateController = require("../controllers/certificateController");
const authMiddleware = require("../middleware/authMiddleware");
// Generate new certificate
router.post(
  "/generate",
  [authMiddleware()],
  certificateController.generateCertificate
);

// Download certificate
router.get(
  "/download/:fileName",
  [authMiddleware()],
  certificateController.downloadCertificate
);

// Verify certificate
router.get(
  "/verify",
  [authMiddleware()],
  certificateController.verifyCertificate
);

// Check certificate
router.get(
  "/check",
  [authMiddleware()],
  certificateController.checkCertificate
);

// List certificates (with pagination)
router.get("/list", [authMiddleware()], certificateController.listCertificates);

// Revoke certificate
router.post(
  "/revoke/:certificateNumber",
  [authMiddleware()],
  certificateController.revokeCertificate
);

module.exports = router;

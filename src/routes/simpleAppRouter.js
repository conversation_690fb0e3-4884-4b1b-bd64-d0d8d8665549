const express = require("express");
const {
  addSimpleApp,
  getSimpleApps,
  getSimpleAppBySlug,
  getSimpleAppById,
  editSimpleApp,
  deleteSimpleApp,
} = require("../controllers/SimpleAppController");
const validatePagination = require("../middleware/validatePagination");
const authMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

router
  .route("/")
  .post(authMiddleware(), addSimpleApp)
  .get(validatePagination, authMiddleware(), getSimpleApps);

router.route("/slug/:slug").get(authMiddleware(), getSimpleAppBySlug);

router
  .route("/:id")
  .get(authMiddleware(), getSimpleAppById)
  .patch(authMiddleware(), editSimpleApp)
  .delete(authMiddleware(), deleteSimpleApp);

module.exports = router;

const express = require("express");
const {
  addCompany,
  deleteCompany,
  getCompany,
  updateCompany,
} = require("../controllers/CompanyController");

const router = express.Router();

router.route("/").post(addCompany);
router.route("/").get(getCompany);
router.route("/:id").delete(deleteCompany);
router.route("/:id").get(getCompany);
router.route("/:id").patch(updateCompany);

module.exports = router;

const express = require("express");
const router = express.Router();
const courseTrackingController = require("../controllers/CourseTrackingController");
const authMiddleware = require("../middleware/authMiddleware");

router.post(
  "/courses/:courseId/tracking",
  authMiddleware(),
  courseTrackingController.updateCourseProgress
);

router.get(
  "/courses/:courseId/tracking",
  authMiddleware(),
  courseTrackingController.getCourseProgress
);

router.get(
  "/:userId",
  authMiddleware(),
  courseTrackingController.getUserCoursesProgress
);

router.post(
  "/courses/:courseId/complete",
  courseTrackingController.completeCourse
);

module.exports = router;

const express = require("express");
const router = express.Router();
const APIRequestLimitController = require("../controllers/APIRequestLimitController");
const auth = require("../middleware/authMiddleware");

// Kullanıcı rotaları - Kullanıcı kendi limitlerini görebilir
router.get("/my-limits", auth(), APIRequestLimitController.getMyLimits);

// Admin rotaları - Sadece admin kullanıcılar erişebilir
// Tüm kullanıcıların limitlerini listeler
router.get("/", auth(true), APIRequestLimitController.getAllLimits);

// Bel<PERSON>li bir kullanıcının limitlerini getirir
router.get(
  "/:userId/:endpoint?",
  auth(true),
  APIRequestLimitController.getUserLimit
);

// Kullanıcı limitlerini günceller
router.patch(
  "/:userId/:endpoint?",
  auth(true),
  APIRequestLimitController.updateUserLimit
);

// Kullanıcı sayaçlarını sıfırlar
router.post(
  "/reset/:userId/:endpoint?",
  auth(true),
  APIRequestLimitController.resetUserCounter
);

// Tüm kullanıcıların sayaçlarını sıfırlar (toplu işlem)
router.post(
  "/reset-all",
  auth(true),
  APIRequestLimitController.resetAllCounters
);

module.exports = router;

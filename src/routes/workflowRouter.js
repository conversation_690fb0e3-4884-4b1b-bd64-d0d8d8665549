const express = require("express");
const {
  addWorkflow,
  getWorkflows,
  getWorkflowBySlug,
  getWorkflowById,
  editWorkflow,
  deleteWorkflow,
} = require("../controllers/WorkflowController");
const validatePagination = require("../middleware/validatePagination");
const authMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

router
  .route("/")
  .post(authMiddleware(), addWorkflow)
  .get(validatePagination, authMiddleware(), getWorkflows);

router.route("/slug/:slug").get(authMiddleware(), getWorkflowBySlug);

router
  .route("/:id")
  .get(authMiddleware(), getWorkflowById)
  .patch(authMiddleware(), editWorkflow)
  .delete(authMiddleware(), deleteWorkflow);

module.exports = router;

const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const {
  getQuizProgress,
  saveQuizProgress,
  getQuizStats,
} = require("../controllers/quizProgressController");

// Quiz ilerlemesini getir
router.get(
  "/courses/:courseId/chapters/:chapterId/topics/:topicId/quiz-progress",
  authMiddleware(),
  getQuizProgress
);

// Quiz ilerlemesini kaydet
router.post(
  "/courses/:courseId/chapters/:chapterId/topics/:topicId/quiz-progress",
  authMiddleware(),
  saveQuizProgress
);

// Quiz istatistiklerini getir
router.get("/courses/:courseId/quiz-stats", authMiddleware(), getQuizStats);

module.exports = router;

const express = require("express");
const {
  allUsers,
  getUserbyEmail,
  inviteUsers,
  getMailTemplate,
  updateJourneyLevel,
  updateAccountById,
  getJourneyLevelById,
  deleteUser,
  getUserById,
  updateUserById,
  deleteAllDatasbyUser,
} = require("../controllers/UserController");
const AuthMiddleware = require("../middleware/authMiddleware");
const {
  updateOnboarding,
  getOnboardingById,
  deleteOnboarding,
  updateOnboardingIndustry,
} = require("../controllers/OnboardingController");

const router = express.Router();

router.route("/").get(AuthMiddleware(), allUsers);
router.route("/:id").delete(AuthMiddleware(), deleteUser);
router.route("/:id").get(AuthMiddleware(), getUserById);
router.route("/:id").patch(AuthMiddleware(), updateUserById);

router.route("/getUserbyEmail").get(AuthMiddleware(), getUserbyEmail);

router.route("/inviteUsers").post(AuthMiddleware(), inviteUsers);
router.route("/getMailTemplate").post(AuthMiddleware(), getMailTemplate);
router.route("/onboarding/:id").patch(AuthMiddleware(), updateOnboarding);
router.route("/onboarding/:id").delete(AuthMiddleware(), deleteOnboarding);
router.route("/onboarding/:id").get(AuthMiddleware(), getOnboardingById);
router
  .route("/onboarding/:id/industry")
  .patch(AuthMiddleware(), updateOnboardingIndustry);
router.route("/journey-level/:id").patch(AuthMiddleware(), updateJourneyLevel);
router.route("/journey-level/:id").get(AuthMiddleware(), getJourneyLevelById);
router.route("/update-account/:id").patch(AuthMiddleware(), updateAccountById);

router
  .route("/delete-all-datas/:id")
  .delete(AuthMiddleware(), deleteAllDatasbyUser);

module.exports = router;

const express = require("express");
const {
  createUser,
  login,
  loginWithToken,
  verifyAccount,
  forgotPassword,
  forgotPasswordChange,
  checkOTP,
  changePassword,
  definePassword,
  me,
  resetPassword,
} = require("../controllers/AuthController");

const router = express.Router();

router.route("/sign-up").post(createUser);
router.route("/sign-in").post(login);
router.route("/me").get(me);
router.route("/login-with-token").post(loginWithToken);
router.route("/verifyAccount").post(verifyAccount);
router.route("/forgotPassword").post(forgotPassword);
router.route("/forgotPasswordChange").post(forgotPasswordChange);
router.route("/checkOTP").post(checkOTP);
router.route("/changePassword").post(changePassword);
router.route("/definePassword").post(definePassword);
router.route("/resetPassword").post(resetPassword);

module.exports = router;

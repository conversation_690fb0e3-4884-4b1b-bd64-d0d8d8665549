const express = require("express");
const {
  getProjects,
  getIdeations,
  getIdeationDescription,
  getQuestionList,
  getAnalyticData,
  getTabFilterData,
} = require("../controllers/IdeationController");

const router = express.Router();

router.route("/getProjects").post(getProjects);
router.route("/getIdeations").post(getIdeations);
router.route("/getIdeationDescription").post(getIdeationDescription);
router.route("/getQuestionList").post(getQuestionList);
router.route("/getAnalyticData").post(getAnalyticData);
router.route("/getTabFilterData").post(getTabFilterData);

module.exports = router;

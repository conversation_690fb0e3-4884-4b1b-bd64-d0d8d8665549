const express = require("express");
const {
  addPermission,
  addRole,
  getRoles,
  getPermissions,
  updatePermission,
} = require("../controllers/PermissionsController");

const router = express.Router();

router.route("/addPermission").post(addPermission);
router.route("/getPermissions").post(getPermissions);
router.route("/updatePermission").post(updatePermission);
router.route("/addRole").post(addRole);
router.route("/getRoles").get(getRoles);

module.exports = router;

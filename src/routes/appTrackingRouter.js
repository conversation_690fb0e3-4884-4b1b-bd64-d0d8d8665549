const express = require("express");
const {
  updateAppTracking,
  getAppTracking,
  mostUsedApp,
  getUsecaseUsageStats,
} = require("../controllers/AppTrackingController");
const authMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

router
  .route("/")
  .patch(authMiddleware(), updateAppTracking)
  .get(authMiddleware(), getAppTracking);
router.get("/most-used/:userId", mostUsedApp);
router.get("/usecase-usage/:userId", getUsecaseUsageStats);

module.exports = router;

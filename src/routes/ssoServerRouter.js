const express = require('express');
const { 
    authorize, 
    login, 
    token, 
    getProtectedResource,
    createClientCredentials,
    getUserStacks,
    getAuthData
} = require('../controllers/SSOServerController');
const { verifySSOToken } = require('../middleware/ssoServerAuthMiddleware');

const router = express.Router();

router.get('/authorize', verifySSOToken, authorize);
router.post('/login', login);
router.post('/token', token);
router.get('/resources', verifySSOToken, getProtectedResource);
router.post('/client-credentials', createClientCredentials);
router.get('/user-stacks/:userId', getUserStacks);
router.get('/auth-data', getAuthData);

module.exports = router; 
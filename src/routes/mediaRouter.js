const express = require("express");

const MediaController = require("../controllers/MediaController");
const auth = require("../middleware/authMiddleware");

const router = express.Router();

router.post("/addMedia", auth(), MediaController.addMedia);
router.get("/listMedia", auth(), MediaController.listMedia);
router.delete("/deleteMedia", auth(), MediaController.deleteMedia);

module.exports = router;

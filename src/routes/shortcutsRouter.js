const express = require("express");
const {
  updateShortcut,
  getShortcuts,
  deleteShortcut,
} = require("../controllers/ShortcutController");
const authMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

router
  .route("/")
  .put(authMiddleware(), updateShortcut)
  .get(authMiddleware(), getShortcuts);

// İki parametreli silme endpoint'i
router.delete("/:shortcutId/:userId", authMiddleware(), deleteShortcut);

module.exports = router;

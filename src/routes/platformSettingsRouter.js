const express = require("express");
const {
  getGeneralSettings,
  getUserSettings,
  getSecuritySettings,
  getSsoSettings,
  updateGeneralSettings,
  updateUserSettings,
  updateSecuritySettings,
  updateSsoSettings,
  getUserSegmentation,
  updateUserSegmentation,
  createSegment,
  updateSegment,
  deleteSegment,
  getSegmentById,
  getPublicSettings,
} = require("../controllers/PlatformSettingsController");
const authMiddleware = require("../middleware/authMiddleware");

const router = express.Router();

// General Settings Routes
router
  .route("/general")
  .get(authMiddleware(), getGeneralSettings)
  .put(authMiddleware(), updateGeneralSettings);

// User Settings Routes
router
  .route("/user")
  .get(authMiddleware(), getUserSettings)
  .put(authMiddleware(), updateUserSettings);

// Security Settings Routes
router
  .route("/security")
  .get(authMiddleware(), getSecuritySettings)
  .put(authMiddleware(), updateSecuritySettings);

// Segment Management Routes
router.route("/user-segmentation").get(authMiddleware(), getUserSegmentation);

router
  .route("/user-segmentation/segments")
  .post(authMiddleware(), createSegment);

router
  .route("/user-segmentation/segments/:segmentId")
  .get(authMiddleware(), getSegmentById)
  .put(authMiddleware(), updateSegment)
  .delete(authMiddleware(), deleteSegment);

//SSO Settings Routes
router
  .route("/sso")
  .get(authMiddleware(), getSsoSettings)
  .put(authMiddleware(), updateSsoSettings);

router.route("/public").get(getPublicSettings);

module.exports = router;

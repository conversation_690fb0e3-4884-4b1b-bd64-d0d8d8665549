const express = require("express");
const router = express.Router();
const reportingController = require("../controllers/ReportingController");
const authMiddleware = require("../middleware/authMiddleware");
const validatePagination = require("../middleware/validatePagination");

/**
 * GET Report Routes - Existing reports
 */
router.get(
  "/training",
  authMiddleware(),
  validatePagination,
  reportingController.getTrainingProgressReport
);

router.get(
  "/ms-training",
  authMiddleware(),
  validatePagination,
  reportingController.getMSTrainingReport
);

router.get(
  "/nextgen",
  authMiddleware(),
  validatePagination,
  reportingController.getNextgenTalentReport
);

router.get(
  "/application",
  authMiddleware(),
  validatePagination,
  reportingController.getApplicationUsageReport
);

router.get(
  "/ai-value",
  authMiddleware(),
  validatePagination,
  reportingController.getAIValueReport
);

router.get(
  "/ideation",
  authMiddleware(),
  validatePagination,
  reportingController.getIdeationReport
);

router.get(
  "/creator",
  authMiddleware(),
  validatePagination,
  reportingController.getCreatorToolsReport
);

/**
 * POST Report Routes - Generate new reports
 */
router.post(
  "/generate/:type",
  authMiddleware(),
  reportingController.createReport
);

// Specific report generation endpoints
router.post("/generate/training", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.post("/generate/nextgen", authMiddleware(), (req, res, next) => {
  return reportingController.createReport(req, res, next);
});

router.get(
  "/list/:reportType/:companyId",
  authMiddleware(),
  (req, res, next) => {
    return reportingController.getReportFromModel(req, res, next);
  }
);

module.exports = router;

const QRCode = require("qrcode");

/**
 * Generates a QR code for certificate verification
 * @param {string} certificateNumber - The unique certificate number
 * @param {string} securityCode - The security code of the certificate
 * @param {string} baseUrl - The base URL for certificate verification
 * @returns {Promise<string>} - Base64 encoded QR code image
 */
async function generateQRCode(certificateNumber, securityCode, baseUrl) {
  try {
    const verificationUrl = `${baseUrl}/verify?cn=${certificateNumber}&sc=${securityCode}`;
    const qrCode = await QRCode.toDataURL(verificationUrl, {
      errorCorrectionLevel: "H",
      margin: 1,
      width: 200,
      color: {
        dark: "#000000",
        light: "#ffffff",
      },
    });
    return qrCode;
  } catch (error) {
    throw new Error("Error generating QR code: " + error.message);
  }
}

module.exports = generateQRCode;

const PDFDocument = require("pdfkit");
const moment = require("moment");
const fs = require("fs");
const path = require("path");

async function generateCertificate(data) {
  return new Promise((resolve, reject) => {
    try {
      // Create new PDF document (A4 size)
      const doc = new PDFDocument({
        size: "A4",
        layout: "landscape",
        margins: { top: 0, bottom: 0, left: 0, right: 0 },
        font: path.join(__dirname, "assets", "Roboto-Regular.ttf"),
      });

      // Collect chunks in buffer
      const chunks = [];
      doc.on("data", (chunk) => chunks.push(chunk));
      doc.on("end", () => resolve(Buffer.concat(chunks)));

      // Asset file paths
      const assetsPath = path.join(__dirname, "assets");
      const logoPath = path.join(assetsPath, "aibs_logo.png");
      const templatePath = path.join(assetsPath, "certificate_template.png");

      // Add template background
      if (fs.existsSync(templatePath)) {
        doc.image(templatePath, 0, 0, {
          width: doc.page.width,
          height: doc.page.height,
          align: "center",
          valign: "center",
        });
      }

      // Add logo (top center)
      if (fs.existsSync(logoPath)) {
        doc.image(logoPath, {
          fit: [250, 250],
          x: (doc.page.width - 250) / 2,
          y: 50,
        });
      }

      // Title
      doc.fontSize(30).text("CERTIFICATE of COMPLETION", 0, 180, {
        align: "center",
      });

      // Subtitle
      doc
        .fontSize(15)
        .text("This certificate to certify that", 0, 230, {
          align: "center",
        })
        .moveDown(2);

      // Full Name
      doc
        .fontSize(20)
        .text(data.fullName, {
          align: "center",
        })
        .moveDown();

      // Add decorative divider
      const startX = doc.page.width / 2 - 250;
      const startY = doc.y;
      doc
        .moveTo(startX, startY)
        .lineTo(startX + 500, startY)
        .lineWidth(1)
        .stroke()
        .moveDown(2);

      doc
        .fontSize(15)
        .text("Successfully completed:", {
          align: "center",
        })
        .moveDown();

      // Certificate description
      doc
        .fontSize(15)
        .text(data.certificateName, {
          align: "center",
        })
        .moveDown();

      // Issue date
      const issueDate = moment(data.issueDate).format("MMMM DD, YYYY");
      doc.fontSize(15).text(`${issueDate}`, {
        align: "center",
      });

      // Certificate number and security code
      doc
        .fontSize(10)
        .text(
          `Certificate No: ${data.certificateNumber}`,
          50,
          doc.page.height - 100
        )
        .text(`Security Code: ${data.securityCode}`, 50, doc.page.height - 85);

      // Footer
      doc
        .fontSize(12)
        .text(
          "This certificate is electronically generated and contains a valid digital signature.",
          {
            align: "center",
            y: doc.page.height - 40,
          }
        );

      // End document
      doc.end();
    } catch (error) {
      console.error("Certificate generation error:", error);
      reject(new Error("Error generating certificate: " + error.message));
    }
  });
}

module.exports = generateCertificate;

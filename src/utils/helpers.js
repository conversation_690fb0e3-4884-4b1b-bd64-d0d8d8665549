const OTP = require("../models/OTPModel");
const { EmailClient } = require("@azure/communication-email");

var crypto = require("crypto");
require("dotenv").config();
const client = new EmailClient(process.env.AZURE_EMAIL_CONNECTION_STRING);

exports.generateUserId = (name) => {
  const randId = Math.floor(Math.random() * 8999 + 1000);
  return `${
    name[0].toUpperCase() + name.slice(1, name.length).split(" ")[0]
  }#${randId}`;
};

exports.httpResponse = (res, statusCode, status, message, data = null) => {
  return res.status(statusCode).json({
    status,
    message,
    data,
  });
};
exports.generateOTP = () => {
  return (OTP = `${Math.floor(1000 + Math.random() * 9000)}`);
};
exports.sendEmail = async ({ to, subject, html, from }) => {
  try {
    const emailMessage = {
      senderAddress:
        typeof from === "string"
          ? from
          : from && from.email
          ? from.email
          : "<EMAIL>",
      content: {
        subject,
        html,
      },
      recipients: {
        to: [
          {
            address: to,
          },
        ],
      },
    };

    const poller = await client.beginSend(emailMessage);
    const result = await poller.pollUntilDone();
    return result;
  } catch (error) {
    console.error("Error in sendEmail:", error);
    throw error;
  }
};
exports.decrypt = (token, method, secret, hmac) => {
  try {
    const computedHmac = crypto
      .createHmac("md5", secret)
      .update(token)
      .digest("hex");

    if (computedHmac === hmac) {
      const iv = Buffer.from(token.substr(0, 24), "base64");
      const encryptedText = Buffer.from(token.substr(24), "base64");

      const decryptor = crypto.createDecipheriv(
        method,
        Buffer.from(secret, "utf8"),
        iv
      );
      let decrypted = decryptor.update(encryptedText);

      decrypted += decryptor.final("utf8");
      return decrypted;
    } else {
      throw new Error("HMAC validation failed");
    }
  } catch (err) {
    throw new Error("Decryption failed");
  }
};

exports.signHmacMd5 = (key, data) => {
  const hmac = crypto.createHmac("md5", key);
  hmac.update(data, "utf-8");
  return hmac.digest("hex");
};

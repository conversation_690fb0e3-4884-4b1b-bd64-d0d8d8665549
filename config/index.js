const dotenv = require("dotenv");
const path = require("path");

const environment = process.env.NODE_ENV || "local";
console.log("NODE_ENV değeri:", process.env.NODE_ENV);
console.log("Kullanılan environment değeri:", environment);
console.log("Aranacak .env dosyası:", `${environment}.env`);
const envPath = path.resolve(__dirname, `${environment}.env`);

dotenv.config({ path: envPath });

module.exports = {
  NODE_ENV: environment,
  PORT: process.env.PORT,
  HTTP_PORT: process.env.HTTP_PORT,
  MONGODB_URI: process.env.MONGODB_URI,
  JWT_SECRET: process.env.JWT_SECRET,
  AWS_REGION: process.env.AWS_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AZURE_EMAIL_CONNECTION_STRING: process.env.AZURE_EMAIL_CONNECTION_STRING,
  DB_HOST: process.env.DB_HOST,
  DB_USER: process.env.DB_USER,
  DB_NAME: process.env.DB_NAME,
  DB_PASSWORD: process.env.DB_PASSWORD,
  AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_STORAGE_CONNECTION_STRING,
  AZURE_STORAGE_ACCOUNT: process.env.AZURE_STORAGE_ACCOUNT,
  AZURE_AI_TRANSLATOR_KEY: process.env.AZURE_AI_TRANSLATOR_KEY,
  AZURE_AI_TRANSLATOR_ENDPOINT: process.env.AZURE_AI_TRANSLATOR_ENDPOINT,
  AZURE_AI_TRANSLATOR_REGION: process.env.AZURE_AI_TRANSLATOR_REGION,
  CDS_API_KEY: process.env.CDS_API_KEY,
  CDS_URL: process.env.CDS_URL,
  // API Keys for x-api-key authentication
  API_KEY_ADMIN: process.env.API_KEY_ADMIN
    ? process.env.API_KEY_ADMIN.trim()
    : null,
  API_KEY_REPORTING: process.env.API_KEY_REPORTING
    ? process.env.API_KEY_REPORTING.trim()
    : null,
  API_KEY_READONLY: process.env.API_KEY_READONLY
    ? process.env.API_KEY_READONLY.trim()
    : null,
};

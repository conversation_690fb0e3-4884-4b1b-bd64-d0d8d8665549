## local
 MONGODB_URI=mongodb://localhost:27017/aibs_management_panel

PORT=3001
HTTP_PORT= 9982
JWT_SECRET=d8154b27a59d61a806d44fec819692e291734445bfb9496a8799a59f115965c4caf4fffe049c82b34d4cf079c3cc588466ed9ee9c367aab6950a7c75fef12e0e
AWS_REGION= email-smtp.eu-central-1.amazonaws.com
AWS_ACCESS_KEY_ID =********************
AWS_SECRET_ACCESS_KEY =kgZILNjo9L6bnQUnR6gZsUbYaJQjaRC955iIalJc
CRYPTO_TOKEN=d8154b27a59d61a8
AITRAINER_API_KEY = 4c36ecb8-578c-4018-aa18-739a31ba39e2
AITRAINER_API_TOKEN =  Wk5JeWhkNG5SQ0U5cXVKaA==DYfLZZ3fe/kVxnZy1kEnTlfiACkRPluDyzVL1dStwTfYLXoDV7cgW2RYeN5wrHFuzFChHMgfcg9shbFEsnGQ2b4SPZvvory/q/ZmYNoLAzEn5bA5a3oaXFstQH74L9Okec0hzFJK8E1aOoiQn/zcvmBfGT6V/XSm35NNxyB7u57YQ9FOFs+pNLI2XJlR60UmweQBRBEww1JEauh2tfGNIQ==
AZURE_EMAIL_CONNECTION_STRING = endpoint=https://aibs-communication-service.switzerland.communication.azure.com/;accesskey=8XIh2UZ8g6zO7r2B0otqdoqkAlfEKz5eqnsfSrK893NixM5yx5IxJQQJ99BBACULyCp1Y4NFAAAAAZCSF3vQ

AZURE_STORAGE_CONNECTION_STRING= DefaultEndpointsProtocol=https;AccountName=adoptionv2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
AZURE_STORAGE_ACCOUNT = adoptionv2

CDS_API_KEY = pk_live_51NMK8DHKx8kd9L2p3DFG8
CDS_URL= https://api.aibusinessschool.com/api/v1